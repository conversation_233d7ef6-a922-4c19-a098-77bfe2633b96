#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Newmnasa.settings')
django.setup()

from main.models import User, Course
from django.contrib.auth import authenticate

# بيانات الاختبار
API_BASE = "http://127.0.0.1:8000"

def get_auth_token(username, password):
    """الحصول على token للمصادقة"""
    response = requests.post(f"{API_BASE}/api/auth/login/", {
        'username': username,
        'password': password
    })
    if response.status_code == 200:
        data = response.json()
        return data.get('access') or data.get('token')
    else:
        print(f"Login failed: {response.status_code}")
        print(response.text)
        return None

def test_course_publish(course_slug, token):
    """اختبار نشر الكورس"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # جرب نشر الكورس
    data = {'is_published': True}
    response = requests.patch(f"{API_BASE}/api/courses/{course_slug}/", 
                            json=data, headers=headers)
    
    print(f"Response Status: {response.status_code}")
    print(f"Response Content: {response.text}")
    
    return response

if __name__ == "__main__":
    # جرب مع المعلم اللي مش عنده محفظة
    print("=== Testing course publish without wallet ===")
    
    # الحصول على token
    token = get_auth_token('saqer22', 'saqer22')
    if not token:
        print("Failed to get token")
        exit(1)
    
    print(f"Got token: {token[:20]}...")
    
    # جرب نشر الكورس
    response = test_course_publish('test-course', token)
    
    if response.status_code == 400:
        print("✅ Validation working! Course publish blocked.")
        try:
            error_data = response.json()
            if 'محفظة' in error_data.get('error', ''):
                print("✅ Correct error message about wallet!")
            else:
                print(f"❌ Unexpected error: {error_data}")
        except:
            print(f"❌ Could not parse error response: {response.text}")
    else:
        print(f"❌ Expected 400 error, got {response.status_code}")
