"""
نظام تسجيل مراجعة الإدارة - Audit Logger
تم إنشاؤه بواسطة: <PERSON><PERSON>
"""

from django.contrib.auth import get_user_model
from django.utils import timezone
from ..models import AdminAuditLog
import json

User = get_user_model()


class AuditLogger:
    """فئة لتسجيل جميع أعمال الإدارة"""
    
    @staticmethod
    def get_client_ip(request):
        """الحصول على IP الحقيقي للمستخدم"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def get_user_agent(request):
        """الحصول على User Agent"""
        return request.META.get('HTTP_USER_AGENT', '')
    
    @staticmethod
    def log_action(
        admin_user,
        action_type,
        description,
        request=None,
        target_object=None,
        old_values=None,
        new_values=None,
        additional_data=None,
        severity='medium'
    ):
        """
        تسجيل عمل إداري
        
        Args:
            admin_user: المستخدم الذي قام بالعمل
            action_type: نوع العمل (من ACTION_TYPES)
            description: وصف العمل
            request: طلب HTTP (اختياري)
            target_object: الكائن المتأثر (اختياري)
            old_values: القيم القديمة (اختياري)
            new_values: القيم الجديدة (اختياري)
            additional_data: بيانات إضافية (اختياري)
            severity: مستوى الخطورة (اختياري)
        """
        
        # معلومات الـ Admin
        admin_username = admin_user.username if admin_user else 'system'
        admin_email = admin_user.email if admin_user else None
        
        # معلومات الكائن المتأثر
        target_model = None
        target_object_id = None
        target_object_repr = None
        
        if target_object:
            target_model = target_object.__class__.__name__
            target_object_id = str(target_object.pk)
            target_object_repr = str(target_object)
        
        # معلومات تقنية
        ip_address = None
        user_agent = None
        session_key = None
        
        if request:
            ip_address = AuditLogger.get_client_ip(request)
            user_agent = AuditLogger.get_user_agent(request)
            session_key = request.session.session_key
        
        # إنشاء السجل
        audit_log = AdminAuditLog.objects.create(
            admin_user=admin_user,
            admin_username=admin_username,
            admin_email=admin_email,
            action_type=action_type,
            action_description=description,
            severity=severity,
            target_model=target_model,
            target_object_id=target_object_id,
            target_object_repr=target_object_repr,
            old_values=old_values,
            new_values=new_values,
            additional_data=additional_data,
            ip_address=ip_address,
            user_agent=user_agent,
            session_key=session_key,
        )
        
        return audit_log
    
    @staticmethod
    def log_dashboard_login(admin_user, request):
        """تسجيل دخول Dashboard"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='dashboard_login',
            description=f'تسجيل دخول Admin Dashboard بواسطة {admin_user.username}',
            request=request,
            severity='low'
        )
    
    @staticmethod
    def log_dashboard_logout(admin_user, request):
        """تسجيل خروج Dashboard"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='dashboard_logout',
            description=f'تسجيل خروج Admin Dashboard بواسطة {admin_user.username}',
            request=request,
            severity='low'
        )
    
    @staticmethod
    def log_payout_created(admin_user, payout, request=None):
        """تسجيل إنشاء تحويل"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='payout_created',
            description=f'إنشاء تحويل جديد للمعلم {payout.instructor.username} بمبلغ {payout.amount_paid} جنيه',
            request=request,
            target_object=payout,
            new_values={
                'instructor': payout.instructor.username,
                'amount_paid': str(payout.amount_paid),
                'status': payout.status,
            },
            severity='medium'
        )
    
    @staticmethod
    def log_payout_completed(admin_user, payout, request=None):
        """تسجيل إكمال تحويل"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='payout_completed',
            description=f'إكمال تحويل للمعلم {payout.instructor.username} بمبلغ {payout.amount_paid} جنيه',
            request=request,
            target_object=payout,
            old_values={'status': 'pending'},
            new_values={'status': 'completed'},
            severity='high'
        )
    
    @staticmethod
    def log_receipt_uploaded(admin_user, payout, request=None):
        """تسجيل رفع وصل تحويل"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='receipt_uploaded',
            description=f'رفع وصل تحويل للمعلم {payout.instructor.username}',
            request=request,
            target_object=payout,
            additional_data={
                'receipt_uploaded': True,
                'payout_amount': str(payout.amount_paid),
            },
            severity='medium'
        )
    
    @staticmethod
    def log_user_action(admin_user, target_user, action_type, description, request=None, old_values=None, new_values=None):
        """تسجيل عمل متعلق بالمستخدمين"""
        severity_map = {
            'user_created': 'medium',
            'user_updated': 'low',
            'user_deleted': 'high',
            'user_activated': 'medium',
            'user_deactivated': 'high',
        }
        
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type=action_type,
            description=description,
            request=request,
            target_object=target_user,
            old_values=old_values,
            new_values=new_values,
            severity=severity_map.get(action_type, 'medium')
        )
    
    @staticmethod
    def log_course_action(admin_user, course, action_type, description, request=None, old_values=None, new_values=None):
        """تسجيل عمل متعلق بالكورسات"""
        severity_map = {
            'course_approved': 'medium',
            'course_rejected': 'medium',
            'course_suspended': 'high',
            'course_deleted': 'critical',
        }
        
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type=action_type,
            description=description,
            request=request,
            target_object=course,
            old_values=old_values,
            new_values=new_values,
            severity=severity_map.get(action_type, 'medium')
        )
    
    @staticmethod
    def log_security_alert(admin_user, alert_type, description, request=None, additional_data=None):
        """تسجيل تنبيه أمني"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='security_alert',
            description=f'تنبيه أمني: {description}',
            request=request,
            additional_data=additional_data,
            severity='critical'
        )
    
    @staticmethod
    def log_data_export(admin_user, export_type, description, request=None, additional_data=None):
        """تسجيل تصدير بيانات"""
        return AuditLogger.log_action(
            admin_user=admin_user,
            action_type='data_exported',
            description=f'تصدير بيانات: {description}',
            request=request,
            additional_data=additional_data,
            severity='medium'
        )


# دوال مساعدة سريعة
def log_admin_login(user, request):
    """دالة سريعة لتسجيل دخول Admin"""
    return AuditLogger.log_dashboard_login(user, request)

def log_admin_logout(user, request):
    """دالة سريعة لتسجيل خروج Admin"""
    return AuditLogger.log_dashboard_logout(user, request)

def log_payout_action(admin_user, payout, action, request=None):
    """دالة سريعة لتسجيل أعمال التحويلات"""
    if action == 'created':
        return AuditLogger.log_payout_created(admin_user, payout, request)
    elif action == 'completed':
        return AuditLogger.log_payout_completed(admin_user, payout, request)
    elif action == 'receipt_uploaded':
        return AuditLogger.log_receipt_uploaded(admin_user, payout, request)
