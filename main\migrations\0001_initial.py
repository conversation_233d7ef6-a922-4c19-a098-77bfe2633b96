# Generated by Django 4.2.20 on 2025-07-14 15:40

import cloudinary.models
from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True)),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='profile_images/')),
                ('bio', models.TextField(blank=True, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('first_name', models.CharField(blank=True, max_length=30, null=True)),
                ('last_name', models.CharField(blank=True, max_length=30, null=True)),
                ('has_wallet', models.BooleanField(default=False)),
                ('reset_token', models.CharField(blank=True, max_length=100, null=True)),
                ('email_verified', models.BooleanField(default=False)),
                ('verification_token', models.CharField(blank=True, max_length=100, null=True)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('is_instructor', models.BooleanField(default=False)),
                ('is_student', models.BooleanField(default=True)),
                ('language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=10)),
                ('wallet_number', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('payment_method', models.CharField(blank=True, choices=[('vodafone_cash', 'فودافون كاش'), ('orange_money', 'أورانج موني'), ('we_cash', 'وي كاش'), ('etisalat_cash', 'اتصالات كاش')], max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='mnasa_users', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='mnasa_users', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'permissions': [('can_teach', 'Can create and teach courses')],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Achievement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('icon', models.CharField(default='🏆', max_length=50)),
                ('achievement_type', models.CharField(choices=[('lessons_completed', 'إكمال دروس'), ('quizzes_passed', 'نجاح في اختبارات'), ('courses_completed', 'إكمال دورات'), ('login_streak', 'دخول متتالي'), ('points_earned', 'كسب نقاط'), ('perfect_score', 'درجة كاملة'), ('fast_completion', 'إكمال سريع'), ('first_course', 'أول دورة'), ('review_given', 'كتابة تقييم')], max_length=30)),
                ('required_count', models.PositiveIntegerField(default=1)),
                ('points_reward', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('is_hidden', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['achievement_type', 'required_count'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(blank=True, null=True, unique=True)),
                ('description', models.TextField()),
                ('short_description', models.CharField(blank=True, max_length=300)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('language', models.CharField(default='Arabic', max_length=20)),
                ('level', models.CharField(choices=[('beginner', 'مبتدئ'), ('intermediate', 'متوسط'), ('advanced', 'متقدم')], default='beginner', max_length=20)),
                ('prerequisites', models.TextField(blank=True)),
                ('learning_outcomes', models.TextField(blank=True)),
                ('max_students', models.PositiveIntegerField(blank=True, null=True)),
                ('is_published', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('thumbnail', cloudinary.models.CloudinaryField(max_length=255, verbose_name='image')),
                ('promo_video', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True)),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='courses', to='main.category')),
                ('instructor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='taught_courses', to=settings.AUTH_USER_MODEL)),
                ('likes', models.ManyToManyField(blank=True, related_name='liked_courses', to=settings.AUTH_USER_MODEL)),
                ('students', models.ManyToManyField(blank=True, related_name='enrolled_courses', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DigitalProduct',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('file', models.FileField(upload_to='digital_products/')),
                ('thumbnail', models.ImageField(upload_to='product_thumbnails/')),
                ('download_limit', models.PositiveIntegerField(default=3)),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Lesson',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(default='درس جديد', max_length=200)),
                ('order', models.PositiveIntegerField(default=1)),
                ('lesson_type', models.CharField(choices=[('video', 'فيديو'), ('article', 'مقال'), ('quiz', 'اختبار'), ('assignment', 'واجب')], default='video', max_length=20)),
                ('content', models.TextField(default='')),
                ('is_preview', models.BooleanField(default=False)),
                ('video', cloudinary.models.CloudinaryField(blank=True, help_text='فيديو ترويجي مجاني على Cloudinary - زكي الخولي', max_length=255, null=True)),
                ('video_public_id', models.CharField(blank=True, help_text='معرف Cloudinary للفيديو الترويجي - زكي الخولي', max_length=255, null=True)),
                ('bunny_video_id', models.CharField(blank=True, help_text='معرف Bunny Stream للفيديو المحمي - زكي الخولي', max_length=255, null=True)),
                ('bunny_video_title', models.CharField(blank=True, help_text='عنوان الفيديو في Bunny Stream - زكي الخولي', max_length=255, null=True)),
                ('bunny_thumbnail_url', models.URLField(blank=True, help_text='رابط الصورة المصغرة من Bunny Stream - زكي الخولي', null=True)),
                ('duration', models.PositiveIntegerField(default=0, help_text='Duration in minutes')),
                ('video_duration', models.PositiveIntegerField(default=0)),
                ('is_drm_protected', models.BooleanField(default=True, help_text='حماية الفيديو باستخدام DRM - زكي الخولي')),
                ('is_hls_encrypted', models.BooleanField(default=True, help_text='تشفير HLS باستخدام AES-128 - زكي الخولي')),
                ('token_expiry_hours', models.PositiveIntegerField(default=24, help_text='مدة صلاحية توكن الوصول - زكي الخولي')),
                ('watermark_enabled', models.BooleanField(default=True, help_text='تفعيل العلامة المائية - زكي الخولي')),
                ('video_platform', models.CharField(choices=[('cloudinary', 'Cloudinary (ترويجي مجاني)'), ('bunny_stream', 'Bunny Stream (محمي مدفوع)')], default='bunny_stream', help_text='منصة استضافة الفيديو - زكي الخولي', max_length=20)),
                ('resources', cloudinary.models.CloudinaryField(blank=True, max_length=255, null=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lessons', to='main.course')),
            ],
            options={
                'ordering': ['order'],
                'unique_together': {('course', 'order')},
            },
        ),
        migrations.CreateModel(
            name='MainCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('paymob_order_id', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('billing_name', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_address', models.TextField(blank=True, null=True)),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to='main.course')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='orders', to='main.digitalproduct')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('max_score', models.PositiveIntegerField(default=100, help_text='الدرجة النهائية للامتحان')),
                ('time_limit', models.IntegerField(default=0)),
                ('quiz_type', models.CharField(choices=[('exam', 'امتحان'), ('assignment', 'واجب')], default='exam', max_length=20)),
                ('is_published', models.BooleanField(default=False)),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='main.lesson')),
            ],
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('comment', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('reply', models.TextField(blank=True, default='')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='main.course')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserQuizAttempt',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('score', models.PositiveIntegerField(default=0)),
                ('passed', models.BooleanField(default=False)),
                ('answers', models.JSONField(blank=True, default=dict)),
                ('submitted', models.BooleanField(default=False)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(auto_now_add=True)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attempts', to='main.quiz')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_attempts', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StudentPoints',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('total_points', models.PositiveIntegerField(default=0)),
                ('available_points', models.PositiveIntegerField(default=0)),
                ('spent_points', models.PositiveIntegerField(default=0)),
                ('current_level', models.CharField(choices=[('bronze', 'البرونزي'), ('silver', 'الفضي'), ('gold', 'الذهبي'), ('platinum', 'البلاتيني'), ('diamond', 'الماسي')], default='bronze', max_length=20)),
                ('lessons_completed', models.PositiveIntegerField(default=0)),
                ('quizzes_passed', models.PositiveIntegerField(default=0)),
                ('courses_completed', models.PositiveIntegerField(default=0)),
                ('login_streak', models.PositiveIntegerField(default=0)),
                ('last_login_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('student', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='points_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ReviewComment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('text', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('parent', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='main.reviewcomment')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='main.review')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review_comments', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('multiple_choice', 'Multiple Choice'), ('true_false', 'True/False')], max_length=20)),
                ('text', models.TextField()),
                ('image', models.ImageField(blank=True, help_text='صورة اختيارية للسؤال', null=True, upload_to='question_images/')),
                ('points', models.PositiveIntegerField(default=1)),
                ('order', models.PositiveIntegerField()),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='main.quiz')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='PointsHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('points', models.IntegerField()),
                ('action', models.CharField(choices=[('earned', 'مكتسبة'), ('spent', 'مستخدمة'), ('bonus', 'مكافأة'), ('penalty', 'خصم')], max_length=20)),
                ('reason', models.CharField(max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.course')),
                ('lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.lesson')),
                ('quiz', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.quiz')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='points_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('credit_card', 'Credit Card'), ('paypal', 'PayPal'), ('bank_transfer', 'Bank Transfer'), ('vodafone_cash', 'Vodafone Cash')], max_length=50)),
                ('transaction_id', models.CharField(max_length=100)),
                ('status', models.CharField(max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('payment_details', models.JSONField(blank=True, null=True)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='main.order')),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('link', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('type', models.CharField(default='SYSTEM', max_length=50)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='LessonAnalytics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('unique_viewers', models.PositiveIntegerField(default=0)),
                ('average_watch_time', models.PositiveIntegerField(default=0)),
                ('completion_rate', models.FloatField(default=0.0)),
                ('common_drop_points', models.JSONField(blank=True, default=list)),
                ('replay_segments', models.JSONField(blank=True, default=list)),
                ('average_rating', models.FloatField(default=0.0)),
                ('difficulty_rating', models.FloatField(default=0.0)),
                ('peak_viewing_hours', models.JSONField(blank=True, default=list)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('lesson', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='main.lesson')),
            ],
        ),
        migrations.CreateModel(
            name='InstructorTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('done', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='InstructorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('specialization', models.CharField(max_length=100)),
                ('qualifications', models.TextField()),
                ('website', models.URLField(blank=True, default='', max_length=255)),
                ('linkedin', models.URLField(blank=True, default='', max_length=255)),
                ('is_approved', models.BooleanField(default=False)),
                ('payment_details', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='instructor_profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='InstructorAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.CharField(choices=[('saturday', 'Saturday'), ('sunday', 'Sunday'), ('monday', 'Monday'), ('tuesday', 'Tuesday'), ('wednesday', 'Wednesday'), ('thursday', 'Thursday'), ('friday', 'Friday')], max_length=10)),
                ('from_time', models.CharField(max_length=10)),
                ('to_time', models.CharField(max_length=10)),
                ('timezone', models.CharField(default='Africa/Cairo', max_length=50)),
                ('enabled', models.BooleanField(default=True)),
                ('note', models.CharField(blank=True, default='بدون ملاحظات', max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availabilities', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('question', models.CharField(max_length=200)),
                ('answer', models.TextField()),
                ('order', models.PositiveIntegerField(default=1)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='faqs', to='main.course')),
            ],
        ),
        migrations.CreateModel(
            name='Certificate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('issued_at', models.DateTimeField(auto_now_add=True)),
                ('certificate_url', models.URLField()),
                ('verification_code', models.CharField(max_length=20, unique=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='certificates', to='main.course')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='certificates', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='category',
            name='main_category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='main.maincategory'),
        ),
        migrations.CreateModel(
            name='Assignment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('instructions', models.TextField(help_text='تعليمات تفصيلية للطالب')),
                ('assignment_type', models.CharField(choices=[('file_upload', 'رفع ملف'), ('text_submission', 'إرسال نص'), ('project', 'مشروع'), ('presentation', 'عرض تقديمي'), ('code_submission', 'كود برمجي'), ('design', 'تصميم')], max_length=30)),
                ('max_score', models.PositiveIntegerField(default=100)),
                ('passing_score', models.PositiveIntegerField(default=70)),
                ('due_date', models.DateTimeField()),
                ('late_submission_allowed', models.BooleanField(default=True)),
                ('late_penalty_percentage', models.PositiveIntegerField(default=10)),
                ('allowed_file_types', models.CharField(default='pdf,doc,docx,txt,zip', help_text='أنواع الملفات المسموحة مفصولة بفاصلة', max_length=200)),
                ('max_file_size_mb', models.PositiveIntegerField(default=10)),
                ('is_published', models.BooleanField(default=False)),
                ('auto_grade', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='main.lesson')),
            ],
            options={
                'ordering': ['due_date'],
            },
        ),
        migrations.CreateModel(
            name='Answer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('is_correct', models.BooleanField(default=False)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='main.question')),
            ],
        ),
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='announcements', to='main.course')),
            ],
        ),
        migrations.CreateModel(
            name='StudentProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('not_started', 'لم يبدأ'), ('in_progress', 'جاري المشاهدة'), ('completed', 'مكتمل'), ('skipped', 'تم التخطي')], default='not_started', max_length=20)),
                ('watch_time', models.PositiveIntegerField(default=0, help_text='الوقت المشاهد بالثواني')),
                ('total_duration', models.PositiveIntegerField(default=0, help_text='المدة الإجمالية بالثواني')),
                ('completion_percentage', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('last_session_start', models.DateTimeField(blank=True, help_text='بداية آخر جلسة مشاهدة', null=True)),
                ('session_timeout_minutes', models.PositiveIntegerField(default=30, help_text='انتهاء الجلسة بعد دقائق من عدم النشاط')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_progress', to='main.course')),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_progress', to='main.lesson')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_progress', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['course', 'lesson__order'],
                'unique_together': {('student', 'lesson')},
            },
        ),
        migrations.CreateModel(
            name='StudentAchievement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('earned_at', models.DateTimeField(auto_now_add=True)),
                ('progress_when_earned', models.PositiveIntegerField(default=0)),
                ('achievement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='student_achievements', to='main.achievement')),
                ('related_course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.course')),
                ('related_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.lesson')),
                ('related_quiz', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.quiz')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='achievements', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-earned_at'],
                'unique_together': {('student', 'achievement')},
            },
        ),
        migrations.CreateModel(
            name='ReviewSchedule',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content_type', models.CharField(choices=[('lesson', 'درس'), ('quiz', 'اختبار'), ('vocabulary', 'مفردات'), ('concept', 'مفهوم')], default='lesson', max_length=20)),
                ('content_title', models.CharField(max_length=200)),
                ('content_summary', models.TextField(blank=True)),
                ('initial_learned_date', models.DateTimeField()),
                ('next_review_date', models.DateTimeField()),
                ('review_interval_days', models.PositiveIntegerField(default=1)),
                ('review_count', models.PositiveIntegerField(default=0)),
                ('success_count', models.PositiveIntegerField(default=0)),
                ('difficulty_level', models.PositiveIntegerField(default=1, help_text='مستوى الصعوبة من 1 إلى 5', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('is_active', models.BooleanField(default=True)),
                ('is_mastered', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('lesson', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review_schedules', to='main.lesson')),
                ('quiz', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='review_schedules', to='main.quiz')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review_schedules', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['next_review_date'],
                'unique_together': {('student', 'lesson', 'content_type')},
            },
        ),
        migrations.CreateModel(
            name='Enrollment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('enrolled_at', models.DateTimeField(auto_now_add=True)),
                ('completed', models.BooleanField(default=False)),
                ('progress', models.PositiveIntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('last_accessed', models.DateTimeField(auto_now=True)),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='main.course')),
                ('current_lesson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='main.lesson')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('student', 'course')},
            },
        ),
        migrations.CreateModel(
            name='AssignmentSubmission',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('text_content', models.TextField(blank=True)),
                ('file_upload', models.FileField(blank=True, null=True, upload_to='assignment_submissions/')),
                ('submission_notes', models.TextField(blank=True, help_text='ملاحظات الطالب')),
                ('score', models.PositiveIntegerField(blank=True, null=True)),
                ('feedback', models.TextField(blank=True, help_text='تعليقات المعلم')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('submitted', 'مرسل'), ('graded', 'مقيم'), ('returned', 'مُعاد')], default='draft', max_length=20)),
                ('submitted_at', models.DateTimeField(blank=True, null=True)),
                ('graded_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_late', models.BooleanField(default=False)),
                ('attempt_number', models.PositiveIntegerField(default=1)),
                ('assignment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='main.assignment')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignment_submissions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-submitted_at'],
                'unique_together': {('assignment', 'student', 'attempt_number')},
            },
        ),
    ]
