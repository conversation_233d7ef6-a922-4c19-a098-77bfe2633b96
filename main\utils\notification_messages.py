# notification_messages.py
# جميع رسائل الإشعارات موحدة هنا لسهولة الترجمة والصيانة

COURSE_CREATED = lambda title: f"تم إنشاء الدورة: {title} بنجاح. بانتظار مراجعتها."
COURSE_DELETED_STUDENT = lambda title: f"تم حذف الدورة التي كنت مسجلًا بها: {title}"
COURSE_DELETED_INSTRUCTOR = lambda title: f"تم حذف الدورة بنجاح: {title}"
COURSE_UPDATED = lambda title: f"تم تحديث الدورة: {title}"
VIDEO_UPLOADED = lambda lesson_title: f"تم رفع الفيديو بنجاح للدرس: {lesson_title}"
VIDEO_DELETED = lambda lesson_title: f"تم حذف الفيديو من الدرس: {lesson_title}"
ANNOUNCEMENT_NEW = lambda course_title, announcement_title: f"إعلان جديد في دورة: {course_title} - {announcement_title}"
PAYMENT_RECEIVED = lambda order_id: f"تم استلام دفعتك بنجاح للطلب رقم {order_id}."
PAYMENT_FOR_COURSE = lambda course_title: f"تم دفع طلب جديد لدورتك: {course_title}"
CERTIFICATE_ISSUED = lambda course_title: f"تم إصدار شهادة جديدة لك في دورة: {course_title}"
QUIZ_CERTIFICATE_CONGRATS = lambda course_title: f"مبروك! لقد حصلت على شهادة الدورة: {course_title}"
REVIEW_COMMENT_REPLY = lambda course_title: f"رد جديد على تعليقك في مراجعة: {course_title}"
REVIEW_COMMENT_NEW = lambda course_title: f"تم إضافة تعليق جديد على مراجعتك في الدورة: {course_title}"
REVIEW_COMMENT_EDIT = lambda username, course_title: f"{username} قام بتعديل تعليقه على مراجعتك للدورة: {course_title}"
REVIEW_REPLY_EDIT = lambda username, course_title: f"{username} عدّل رده على تعليقك في مراجعة: {course_title}"
REVIEW_COMMENT_DELETE = lambda username, course_title: f"{username} حذف تعليقه من مراجعة: {course_title}"
REVIEW_EDIT = lambda username, course_title: f"{username} قام بتعديل مراجعته لدورتك: {course_title}"
REVIEW_DELETE = lambda username, course_title: f"{username} حذف مراجعته من دورتك: {course_title}"
REVIEW_APPROVED = lambda course_title: f"تمت الموافقة على مراجعتك في الدورة: {course_title}"
