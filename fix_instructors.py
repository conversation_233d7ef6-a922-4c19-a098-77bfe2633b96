#!/usr/bin/env python3
"""
Fix instructor payment information
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Newmnasa.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()

def main():
    """Fix all instructors without payment info"""
    print("🔧 Fixing Instructor Payment Information...")
    print("=" * 60)
    
    # Find instructors without complete payment info
    instructors_without_payment = User.objects.filter(
        is_instructor=True
    ).filter(
        models.Q(wallet_number__isnull=True) | 
        models.Q(payment_method__isnull=True)
    )
    
    print(f"Instructors without complete payment info: {instructors_without_payment.count()}")
    
    for instructor in instructors_without_payment:
        print(f"Fixing {instructor.username}...")
        
        if not instructor.wallet_number:
            instructor.wallet_number = "01234567890"  # Default test wallet
            print(f"   Set wallet_number: {instructor.wallet_number}")
            
        if not instructor.payment_method:
            instructor.payment_method = "vodafone_cash"  # Default payment method
            print(f"   Set payment_method: {instructor.payment_method}")
            
        instructor.has_wallet = True
        instructor.save()
        
        print(f"   ✅ Updated {instructor.username}")
        print()
    
    # Show final status
    print("=" * 60)
    print("Final Status:")
    instructors = User.objects.filter(is_instructor=True)
    
    for instructor in instructors:
        can_create = bool(instructor.wallet_number and instructor.payment_method)
        status = "✅ CAN CREATE COURSES" if can_create else "❌ CANNOT CREATE COURSES"
        print(f"{instructor.username}: {status}")
    
    print("✅ Fix complete!")

if __name__ == "__main__":
    main()
