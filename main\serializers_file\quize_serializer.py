from rest_framework import serializers

from ..models import Quiz, Question, Answer, UserQuizAttempt
from .user_serializer import UserSerializer


class QuizSerializer(serializers.ModelSerializer):
    questions = serializers.SerializerMethodField()

    class Meta:
        model = Quiz
        fields = [
            "id",
            "lesson",
            "title",
            "description",
            "max_score",  # النظام الجديد البسيط - zaki alkholy
            "time_limit",
            "quiz_type",
            "is_published",
            "questions",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        # تحويل passing_score إلى max_score عند الإنشاء - zaki alkholy
        if 'passing_score' in validated_data and 'max_score' not in validated_data:
            validated_data['max_score'] = validated_data['passing_score']
        return super().create(validated_data)

    def update(self, instance, validated_data):
        # تحويل passing_score إلى max_score عند التحديث - z<PERSON> alkh<PERSON>
        if 'passing_score' in validated_data:
            validated_data['max_score'] = validated_data['passing_score']
        return super().update(instance, validated_data)

    def get_questions(self, obj):
        questions = obj.questions.all().order_by("order")
        return QuestionSerializer(questions, many=True, context=self.context).data


class AnswerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Answer
        fields = ["id", "question", "text", "is_correct"]
        read_only_fields = ["id", "created_at", "updated_at"]


# تحديث QuestionSerializer لدعم الصور - زكي الخولي
class QuestionSerializer(serializers.ModelSerializer):
    answers = serializers.SerializerMethodField()
    image_url = (
        serializers.SerializerMethodField()
    )  # إضافة حقل لرابط الصورة - زكي الخولي

    class Meta:
        model = Question
        fields = [
            "id",
            "quiz",
            "text",
            "question_type",
            "points",
            "order",
            "answers",
            "image",
            "image_url",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "image_url"]

    def get_answers(self, obj):
        answers = obj.answers.all()
        return AnswerSerializer(answers, many=True).data

    def get_image_url(self, obj):
        # إرجاع رابط الصورة إذا كانت موجودة - زكي الخولي
        if obj.image:
            request = self.context.get("request")
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None


class UserQuizAttemptSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    quiz = QuizSerializer(read_only=True)

    class Meta:
        model = UserQuizAttempt
        fields = [
            "id",
            "user",
            "quiz",
            "score",
            "passed",
            "answers",
            "submitted",
            "submitted_at",
            "created_at",
            "completed_at",
        ]
        read_only_fields = fields
