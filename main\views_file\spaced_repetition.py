# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

# Django imports
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model

# Models
from ..models import (
    ReviewSchedule,
    Lesson,
    Quiz,
    Question,
    Answer,
    StudentProgress,
    UserQuizAttempt,
    StudentPoints,
)

# Serializers
from ..serializers_file.review_schedule_serializer import (
    ReviewScheduleSerializer,
    ReviewSessionSerializer,
    ReviewAnswerSerializer,
    DailyReviewSerializer,
    CreateReviewScheduleSerializer,
    ReviewStatsSerializer,
)

User = get_user_model()


class ReviewScheduleViewSet(viewsets.ModelViewSet):
    """ViewSet لإدارة جدولة المراجعة المتباعدة"""
    serializer_class = ReviewScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام UUID للحماية القصوى - <PERSON><PERSON>
    
    def get_queryset(self):
        """الحصول على جدولة المراجعة للطالب الحالي"""
        return ReviewSchedule.objects.filter(
            student=self.request.user
        ).select_related('lesson__course', 'quiz')
    
    def get_serializer_class(self):
        """اختيار الـ serializer المناسب"""
        if self.action == 'create':
            return CreateReviewScheduleSerializer
        return ReviewScheduleSerializer
    
    @action(detail=False, methods=['get'])
    def today_reviews(self, request):
        """المراجعات المطلوبة اليوم"""
        today = timezone.now().date()
        
        # المراجعات المجدولة لليوم
        today_reviews = self.get_queryset().filter(
            is_active=True,
            next_review_date__date=today
        )
        
        # المراجعات المتأخرة
        overdue_reviews = self.get_queryset().filter(
            is_active=True,
            next_review_date__date__lt=today
        )
        
        # دمج القوائم
        all_reviews = list(today_reviews) + list(overdue_reviews)
        
        serializer = ReviewScheduleSerializer(all_reviews, many=True)
        
        return Response({
            "today_reviews": ReviewScheduleSerializer(today_reviews, many=True).data,
            "overdue_reviews": ReviewScheduleSerializer(overdue_reviews, many=True).data,
            "total_count": len(all_reviews),
            "stats": {
                "today_count": today_reviews.count(),
                "overdue_count": overdue_reviews.count(),
            }
        })
    
    @action(detail=False, methods=['get'])
    def upcoming_reviews(self, request):
        """المراجعات القادمة في الأسبوع القادم"""
        today = timezone.now().date()
        next_week = today + timedelta(days=7)
        
        upcoming = self.get_queryset().filter(
            is_active=True,
            next_review_date__date__gt=today,
            next_review_date__date__lte=next_week
        ).order_by('next_review_date')
        
        # تجميع حسب اليوم
        reviews_by_day = {}
        for review in upcoming:
            day = review.next_review_date.date()
            if day not in reviews_by_day:
                reviews_by_day[day] = []
            reviews_by_day[day].append(review)
        
        # تحويل لتنسيق مناسب للعرض
        upcoming_data = []
        for day, reviews in reviews_by_day.items():
            upcoming_data.append({
                "date": day,
                "count": len(reviews),
                "reviews": ReviewScheduleSerializer(reviews, many=True).data
            })
        
        # ترتيب حسب التاريخ
        upcoming_data.sort(key=lambda x: x['date'])
        
        return Response({
            "upcoming_reviews": upcoming_data,
            "total_upcoming": upcoming.count(),
        })
    
    @action(detail=True, methods=['get'])
    def start_review_session(self, request, pk=None):
        """بدء جلسة مراجعة"""
        review_schedule = self.get_object()
        
        if not review_schedule.is_active:
            return Response(
                {"error": "هذا العنصر غير نشط للمراجعة"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # تحضير محتوى المراجعة
        session_data = {
            "review_schedule_id": str(review_schedule.id),
            "content_title": review_schedule.content_title,
            "content_summary": review_schedule.content_summary,
            "difficulty_level": review_schedule.difficulty_level,
            "review_count": review_schedule.review_count,
            "questions": []
        }
        
        # إضافة أسئلة إذا كان المحتوى اختبار
        if review_schedule.quiz:
            questions = review_schedule.quiz.questions.all()[:5]  # أول 5 أسئلة
            questions_data = []
            
            for question in questions:
                question_data = {
                    "id": str(question.id),
                    "text": question.text,
                    "question_type": question.question_type,
                    "image": question.image.url if question.image else None,
                    "answers": []
                }
                
                # إضافة الإجابات (بدون إظهار الإجابة الصحيحة)
                for answer in question.answers.all():
                    question_data["answers"].append({
                        "id": str(answer.id),
                        "text": answer.text,
                    })
                
                questions_data.append(question_data)
            
            session_data["questions"] = questions_data
        
        return Response(session_data)
    
    @action(detail=False, methods=['post'])
    def submit_review(self, request):
        """تسليم نتائج المراجعة"""
        serializer = ReviewAnswerSerializer(data=request.data)
        
        if serializer.is_valid():
            result = serializer.save()
            return Response({
                "message": "تم تسليم المراجعة بنجاح",
                "result": result,
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def mastered_items(self, request):
        """العناصر التي تم إتقانها"""
        mastered = self.get_queryset().filter(is_mastered=True)
        
        serializer = ReviewScheduleSerializer(mastered, many=True)
        
        # تجميع حسب نوع المحتوى
        by_type = {}
        for item in mastered:
            content_type = item.content_type
            if content_type not in by_type:
                by_type[content_type] = []
            by_type[content_type].append(item)
        
        return Response({
            "mastered_items": serializer.data,
            "total_mastered": mastered.count(),
            "by_type": {
                content_type: ReviewScheduleSerializer(items, many=True).data
                for content_type, items in by_type.items()
            }
        })


class DailyReviewAPIView(APIView):
    """API للمراجعة اليومية"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على المراجعة اليومية"""
        student = request.user
        
        # استخدام الـ serializer للمراجعة اليومية
        daily_review_data = DailyReviewSerializer(student).data
        
        # إضافة إحصائيات إضافية
        today = timezone.now().date()
        
        # المراجعات المكتملة اليوم (نحتاج لتتبع هذا)
        # يمكن إضافة نموذج ReviewSession لتتبع الجلسات المكتملة
        
        # التقدم الأسبوعي
        week_start = today - timedelta(days=today.weekday())
        weekly_reviews = ReviewSchedule.objects.filter(
            student=student,
            next_review_date__date__gte=week_start,
            next_review_date__date__lte=today
        ).count()
        
        return Response({
            **daily_review_data,
            "weekly_progress": {
                "reviews_this_week": weekly_reviews,
                "week_start": week_start,
            }
        })


class ReviewStatsAPIView(APIView):
    """API لإحصائيات المراجعة"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على إحصائيات المراجعة الشاملة"""
        student = request.user
        
        # استخدام الـ serializer للإحصائيات
        stats_data = ReviewStatsSerializer(student).data
        
        # إضافة تحليلات إضافية
        
        # التقدم الشهري
        current_month = timezone.now().replace(day=1).date()
        monthly_reviews = ReviewSchedule.objects.filter(
            student=student,
            updated_at__date__gte=current_month
        ).count()
        
        # أكثر المواضيع مراجعة
        most_reviewed = ReviewSchedule.objects.filter(
            student=student
        ).values('content_type').annotate(
            count=Count('id'),
            avg_difficulty=Avg('difficulty_level')
        ).order_by('-count')
        
        # الأيام الأكثر نشاطاً
        # يمكن تطوير هذا لاحقاً بإضافة تتبع للجلسات
        
        return Response({
            **stats_data,
            "monthly_reviews": monthly_reviews,
            "most_reviewed_topics": [
                {
                    "content_type": item['content_type'],
                    "count": item['count'],
                    "average_difficulty": round(item['avg_difficulty'], 2)
                }
                for item in most_reviewed
            ],
        })


class AutoReviewSchedulerAPIView(APIView):
    """API لجدولة المراجعة التلقائية"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """إنشاء جدولة مراجعة تلقائية للمحتوى المكتمل"""
        student = request.user
        
        # الحصول على الدروس المكتملة التي لم تُجدول للمراجعة
        completed_lessons = StudentProgress.objects.filter(
            student=student,
            status='completed'
        ).exclude(
            lesson__review_schedules__student=student
        )
        
        # الحصول على الاختبارات الناجحة التي لم تُجدول للمراجعة
        passed_quizzes = UserQuizAttempt.objects.filter(
            user=student,
            passed=True
        ).exclude(
            quiz__review_schedules__student=student
        ).values('quiz').distinct()
        
        created_schedules = []
        
        # جدولة الدروس المكتملة
        for progress in completed_lessons:
            lesson = progress.lesson
            
            # إنشاء جدولة مراجعة للدرس
            review_schedule = ReviewSchedule.objects.create(
                student=student,
                lesson=lesson,
                content_type='lesson',
                content_title=lesson.title,
                content_summary=lesson.description[:200] if lesson.description else '',
                initial_learned_date=progress.completed_at or timezone.now(),
                next_review_date=timezone.now() + timedelta(days=1),
                review_interval_days=1,
            )
            created_schedules.append(review_schedule)
        
        # جدولة الاختبارات الناجحة
        for quiz_data in passed_quizzes:
            try:
                quiz = Quiz.objects.get(id=quiz_data['quiz'])
                
                # الحصول على آخر محاولة ناجحة
                last_attempt = UserQuizAttempt.objects.filter(
                    user=student,
                    quiz=quiz,
                    passed=True
                ).order_by('-created_at').first()
                
                if last_attempt:
                    review_schedule = ReviewSchedule.objects.create(
                        student=student,
                        lesson=quiz.lesson,
                        quiz=quiz,
                        content_type='quiz',
                        content_title=quiz.title,
                        content_summary=quiz.description[:200] if quiz.description else '',
                        initial_learned_date=last_attempt.created_at,
                        next_review_date=timezone.now() + timedelta(days=1),
                        review_interval_days=1,
                    )
                    created_schedules.append(review_schedule)
            
            except Quiz.DoesNotExist:
                continue
        
        return Response({
            "message": f"تم إنشاء {len(created_schedules)} جدولة مراجعة جديدة",
            "created_count": len(created_schedules),
            "schedules": ReviewScheduleSerializer(created_schedules, many=True).data
        })


class ReviewRecommendationsAPIView(APIView):
    """API لتوصيات المراجعة الذكية"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على توصيات المراجعة الذكية"""
        student = request.user
        
        # العناصر التي تحتاج مراجعة عاجلة (صعوبة عالية)
        urgent_reviews = ReviewSchedule.objects.filter(
            student=student,
            is_active=True,
            difficulty_level__gte=4
        ).order_by('next_review_date')[:5]
        
        # العناصر التي لم تُراجع لفترة طويلة
        old_reviews = ReviewSchedule.objects.filter(
            student=student,
            is_active=True,
            next_review_date__lt=timezone.now() - timedelta(days=3)
        ).order_by('next_review_date')[:5]
        
        # المواضيع المقترحة للمراجعة (بناءً على الأداء)
        weak_topics = ReviewSchedule.objects.filter(
            student=student,
            is_active=True
        ).annotate(
            success_rate=F('success_count') * 100.0 / F('review_count')
        ).filter(
            review_count__gt=0,
            success_rate__lt=70
        ).order_by('success_rate')[:5]
        
        return Response({
            "urgent_reviews": ReviewScheduleSerializer(urgent_reviews, many=True).data,
            "overdue_reviews": ReviewScheduleSerializer(old_reviews, many=True).data,
            "weak_topics": ReviewScheduleSerializer(weak_topics, many=True).data,
            "recommendations": [
                {
                    "type": "urgent",
                    "title": "مراجعة عاجلة",
                    "description": "هذه المواضيع تحتاج مراجعة فورية",
                    "count": urgent_reviews.count(),
                },
                {
                    "type": "overdue",
                    "title": "مراجعة متأخرة",
                    "description": "هذه المواضيع متأخرة عن موعد المراجعة",
                    "count": old_reviews.count(),
                },
                {
                    "type": "weak",
                    "title": "مواضيع ضعيفة",
                    "description": "هذه المواضيع تحتاج تركيز إضافي",
                    "count": weak_topics.count(),
                },
            ]
        })
