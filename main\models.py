from django.db import models
from cloudinary.models import Cloudinary<PERSON>ield
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
import uuid
from django.db.models.signals import post_save, post_migrate
from django.dispatch import receiver
from django.utils import timezone
import secrets


class User(AbstractUser):
    # معلومات أساسية
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    profile_image = models.ImageField(
        upload_to="profile_images/", blank=True, null=True
    )
    bio = models.TextField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    first_name = models.CharField(max_length=30, blank=True, null=True)
    last_name = models.Char<PERSON>ield(max_length=30, blank=True, null=True)
    has_wallet = models.BooleanField(default=False)
    reset_token = models.CharField(max_length=100, blank=True, null=True)
    # التحقق والأمان
    email_verified = models.BooleanField(default=False)
    verification_token = models.CharField(max_length=100, blank=True, null=True)
    verification_token_expiry = models.DateTimeField(blank=True, null=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)

    # الأدوار
    is_instructor = models.BooleanField(default=False)
    is_student = models.BooleanField(default=True)

    # تفضيلات المستخدم
    language = models.CharField(
        max_length=10, default="ar", choices=[("ar", "العربية"), ("en", "English")]
    )

    # معلومات الدفع
    wallet_number = models.CharField(max_length=50, blank=True, null=True, unique=True)
    payment_method = models.CharField(
        max_length=50,
        choices=[
            ("vodafone_cash", "فودافون كاش"),
            ("orange_money", "أورانج موني"),
            ("we_cash", "وي كاش"),
            ("etisalat_cash", "اتصالات كاش"),
        ],
        blank=True,
        null=True,
    )

    # علاقات الصلاحيات
    groups = models.ManyToManyField(
        "auth.Group",
        related_name="mnasa_users",
        blank=True,
        help_text="The groups this user belongs to.",
        verbose_name="groups",
    )
    user_permissions = models.ManyToManyField(
        "auth.Permission",
        related_name="mnasa_users",
        blank=True,
        help_text="Specific permissions for this user.",
        verbose_name="user permissions",
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.verification_token:
            self.verification_token = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.username

    class Meta:
        verbose_name = _("user")
        verbose_name_plural = _("users")
        permissions = [
            ("can_teach", "Can create and teach courses"),
        ]


class InstructorProfile(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="instructor_profile"
    )
    specialization = models.CharField(max_length=100)
    qualifications = models.TextField()
    website = models.URLField(max_length=255, default="", blank=True)
    linkedin = models.URLField(max_length=255, default="", blank=True)
    is_approved = models.BooleanField(default=False)
    payment_details = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Instructor Profile for {self.user.username}"


class MainCategory(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.name


class Category(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    main_category = models.ForeignKey(
        MainCategory, on_delete=models.CASCADE, related_name="subcategories"
    )

    def __str__(self):
        return f"{self.name} ({self.main_category.name})"


class Course(models.Model):
    LEVEL_CHOICES = [
        ("beginner", "مبتدئ"),
        ("intermediate", "متوسط"),
        ("advanced", "متقدم"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    slug = models.SlugField(unique=True, null=True, blank=True)
    description = models.TextField()
    short_description = models.CharField(max_length=300, blank=True)
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, null=True, related_name="courses"
    )
    instructor = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="taught_courses"
    )
    students = models.ManyToManyField(User, related_name="enrolled_courses", blank=True)
    likes = models.ManyToManyField(User, related_name="liked_courses", blank=True)

    price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_price = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    currency = models.CharField(max_length=3, default="USD")

    language = models.CharField(max_length=20, default="Arabic")
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default="beginner")
    prerequisites = models.TextField(blank=True)
    learning_outcomes = models.TextField(blank=True)

    max_students = models.PositiveIntegerField(null=True, blank=True)
    is_published = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    thumbnail = CloudinaryField("image")
    promo_video = CloudinaryField(resource_type="video", blank=True, null=True)

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            # إنشاء slug من العنوان - zaki alkholy
            import re
            import random
            import string

            # إذا كان العنوان عربي، استخدم random string
            if any('\u0600' <= char <= '\u06FF' for char in self.title):
                # إنشاء slug عشوائي للنصوص العربية
                random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                self.slug = random_suffix
            else:
                # استخدام slugify للنصوص الإنجليزية
                self.slug = slugify(self.title)

            # التأكد من عدم التكرار
            original_slug = self.slug
            counter = 1
            while Course.objects.filter(slug=self.slug).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)


class Lesson(models.Model):
    LESSON_TYPES = [
        ("video", "فيديو"),
        ("article", "مقال"),
        ("quiz", "اختبار"),
        ("assignment", "واجب"),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="lessons")
    title = models.CharField(max_length=200, default="درس جديد")
    order = models.PositiveIntegerField(default=1)
    lesson_type = models.CharField(max_length=20, choices=LESSON_TYPES, default="video")
    content = models.TextField(default="")

    is_preview = models.BooleanField(default=False)

    # =============================
    # نظام الفيديوهات المختلط - زكي الخولي
    # Cloudinary للفيديوهات الترويجية المجانية
    # Bunny Stream للفيديوهات المدفوعة المحمية
    # =============================

    # Cloudinary للفيديوهات الترويجية (تبقى كما هي) - زكي الخولي
    video = CloudinaryField(
        resource_type="video", folder="course_videos/", blank=True, null=True,
        help_text="فيديو ترويجي مجاني على Cloudinary - زكي الخولي"
    )
    video_public_id = models.CharField(
        max_length=255, blank=True, null=True,
        help_text="معرف Cloudinary للفيديو الترويجي - زكي الخولي"
    )

    # Bunny Stream للفيديوهات المدفوعة المحمية - زكي الخولي
    bunny_video_id = models.CharField(
        max_length=255, blank=True, null=True,
        help_text="معرف Bunny Stream للفيديو المحمي - زكي الخولي"
    )
    bunny_video_title = models.CharField(
        max_length=255, blank=True, null=True,
        help_text="عنوان الفيديو في Bunny Stream - زكي الخولي"
    )
    bunny_thumbnail_url = models.URLField(
        blank=True, null=True,
        help_text="رابط الصورة المصغرة من Bunny Stream - زكي الخولي"
    )

    # إعدادات مشتركة للفيديوهات - زكي الخولي
    duration = models.PositiveIntegerField(help_text="Duration in minutes", default=0)
    video_duration = models.PositiveIntegerField(default=0)

    # إعدادات الحماية والتشفير (خاصة بـ Bunny Stream) - زكي الخولي
    is_drm_protected = models.BooleanField(
        default=True, help_text="حماية الفيديو باستخدام DRM - زكي الخولي"
    )
    is_hls_encrypted = models.BooleanField(
        default=True, help_text="تشفير HLS باستخدام AES-128 - زكي الخولي"
    )
    token_expiry_hours = models.PositiveIntegerField(
        default=24, help_text="مدة صلاحية توكن الوصول - زكي الخولي"
    )
    watermark_enabled = models.BooleanField(
        default=True, help_text="تفعيل العلامة المائية - زكي الخولي"
    )

    # تحديد نوع الفيديو - زكي الخولي
    VIDEO_PLATFORM_CHOICES = [
        ("cloudinary", "Cloudinary (ترويجي مجاني)"),
        ("bunny_stream", "Bunny Stream (محمي مدفوع)"),
    ]
    video_platform = models.CharField(
        max_length=20,
        choices=VIDEO_PLATFORM_CHOICES,
        default="bunny_stream",
        help_text="منصة استضافة الفيديو - زكي الخولي"
    )

    resources = CloudinaryField(
        resource_type="raw",
        folder="lesson_resources/",
        type="upload",  # ✅ يخلي الملف Public
        blank=True,
        null=True,
    )

    class Meta:
        ordering = ["order"]
        unique_together = ("course", "order")

    def __str__(self):
        return f"{self.course.title} - {self.title}"

    # =============================
    # دوال مساعدة للفيديوهات - زكي الخولي
    # =============================

    def is_video_on_bunny_stream(self):
        """التحقق من وجود الفيديو على Bunny Stream - زكي الخولي"""
        return bool(self.bunny_video_id and self.video_platform == "bunny_stream")

    def is_video_on_cloudinary(self):
        """التحقق من وجود الفيديو على Cloudinary - زكي الخولي"""
        return bool(self.video_public_id and self.video_platform == "cloudinary")

    def has_video(self):
        """التحقق من وجود فيديو على أي منصة - زكي الخولي"""
        return self.is_video_on_bunny_stream() or self.is_video_on_cloudinary()

    def get_video_platform_display_ar(self):
        """عرض منصة الفيديو بالعربية - زكي الخولي"""
        platform_map = {
            "cloudinary": "كلاودناري (ترويجي)",
            "bunny_stream": "باني ستريم (محمي)",
        }
        return platform_map.get(self.video_platform, "غير محدد")

    def is_premium_video(self):
        """التحقق من كون الفيديو مدفوع ومحمي - زكي الخولي"""
        return (
            self.video_platform == "bunny_stream"
            and self.bunny_video_id
            and not self.is_preview
        )

    def get_video_security_level(self):
        """الحصول على مستوى الأمان للفيديو - زكي الخولي"""
        if self.is_premium_video():
            security_features = []
            if self.is_drm_protected:
                security_features.append("DRM")
            if self.is_hls_encrypted:
                security_features.append("HLS Encryption")
            if self.watermark_enabled:
                security_features.append("Watermark")

            return {
                "level": "عالي",
                "features": security_features,
                "platform": "Bunny Stream"
            }
        elif self.is_video_on_cloudinary():
            return {
                "level": "أساسي",
                "features": ["Basic Protection"],
                "platform": "Cloudinary"
            }
        else:
            return {
                "level": "بدون حماية",
                "features": [],
                "platform": "غير محدد"
            }


class Enrollment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="enrollments"
    )
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name="enrollments"
    )
    enrolled_at = models.DateTimeField(auto_now_add=True)
    completed = models.BooleanField(default=False)

    progress = models.PositiveIntegerField(
        default=0, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    last_accessed = models.DateTimeField(auto_now=True)
    current_lesson = models.ForeignKey(
        Lesson, on_delete=models.SET_NULL, null=True, blank=True
    )

    class Meta:
        unique_together = ("student", "course")

    def __str__(self):
        return f"{self.student.username} in {self.course.title}"


class Review(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="reviews")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="reviews")
    rating = models.PositiveIntegerField(choices=[(i, i) for i in range(1, 6)])
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=False)
    reply = models.TextField(default="", blank=True)  # كان blank=True, null=True

    def __str__(self):
        return f"Review by {self.user.username} for {self.course.title}"


class ReviewComment(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    review = models.ForeignKey(
        "Review", on_delete=models.CASCADE, related_name="comments"
    )
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="review_comments"
    )
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    parent = models.ForeignKey(
        "self",
        default=None,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name="replies",
    )  # أبقى null=True لأن parent قد يكون None

    def __str__(self):
        return f"Comment by {self.user.username} on review {self.review.id}"


class DigitalProduct(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    seller = models.ForeignKey(User, on_delete=models.CASCADE, related_name="products")

    file = models.FileField(upload_to="digital_products/")
    thumbnail = models.ImageField(upload_to="product_thumbnails/")

    download_limit = models.PositiveIntegerField(default=3)
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title


class PaymentSession(models.Model):
    """جلسة دفع مؤقتة - تتحول لـ Order عند نجاح الدفع"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="payment_sessions")
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="payment_sessions")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    paymob_order_id = models.CharField(max_length=100, unique=True)
    payment_method = models.CharField(max_length=50)
    phone_number = models.CharField(max_length=15)

    # تفاصيل السعر
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    paymob_fee = models.DecimalField(max_digits=10, decimal_places=2)
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2)

    # معلومات الفوترة
    billing_email = models.EmailField(null=True, blank=True)
    billing_name = models.CharField(max_length=100, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()  # انتهاء صلاحية الجلسة

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"Payment Session {self.id} - {self.user.username}"

    def is_expired(self):
        from django.utils import timezone
        return timezone.now() > self.expires_at


class Order(models.Model):
    STATUS_CHOICES = (
        ("completed", "Completed"),
        ("cancelled", "Cancelled"),
        ("refunded", "Refunded"),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="orders")
    course = models.ForeignKey(
        Course, on_delete=models.SET_NULL, null=True, blank=True, related_name="orders"
    )
    product = models.ForeignKey(
        DigitalProduct,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="orders",
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="completed")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    paymob_order_id = models.CharField(max_length=100, blank=True, null=True)

    # تفاصيل السعر
    base_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    paymob_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    billing_email = models.EmailField(null=True, blank=True)
    billing_name = models.CharField(max_length=100, null=True, blank=True)
    billing_address = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"Order {self.id} - {self.user.username}"

    def save(self, *args, **kwargs):
        if not self.billing_email and self.user.email:
            self.billing_email = self.user.email
        if not self.billing_name:
            self.billing_name = self.user.get_full_name() or self.user.username
        super().save(*args, **kwargs)


class Payment(models.Model):
    PAYMENT_METHODS = (
        ("credit_card", "Credit Card"),
        ("paypal", "PayPal"),
        ("bank_transfer", "Bank Transfer"),
        ("vodafone_cash", "Vodafone Cash"),
        ("orange_money", "Orange Money"),
        ("we_cash", "WE Cash"),
        ("etisalat_cash", "Etisalat Cash"),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name="payments")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50, choices=PAYMENT_METHODS)
    transaction_id = models.CharField(max_length=100)
    status = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)

    payment_details = models.JSONField(blank=True, null=True)

    def __str__(self):
        return f"Payment {self.id} - {self.order.id}"


class Certificate(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="certificates"
    )
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name="certificates"
    )
    issued_at = models.DateTimeField(auto_now_add=True)
    certificate_url = models.URLField()
    verification_code = models.CharField(max_length=20, unique=True)

    def __str__(self):
        return f"Certificate for {self.user.username} - {self.course.title}"


class Quiz(models.Model):
    QUIZ_TYPE_CHOICES = [
        ("exam", "امتحان"),
        ("assignment", "واجب"),
    ]
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name="quizzes")
    title = models.CharField(max_length=200)
    description = models.TextField()

    # النظام الجديد البسيط - zaki alkholy
    max_score = models.PositiveIntegerField(default=100, help_text="الدرجة النهائية للامتحان")

    time_limit = models.IntegerField(default=0)
    quiz_type = models.CharField(
        max_length=20, choices=QUIZ_TYPE_CHOICES, default="exam"
    )
    # إضافة حقل النشر
    is_published = models.BooleanField(default=False)

    @property
    def passing_score(self):
        """حساب درجة النجاح = نص الدرجة النهائية - zaki alkholy"""
        return self.max_score / 2

    @property
    def calculated_max_score(self):
        """حساب الدرجة النهائية من مجموع نقاط الأسئلة - zaki alkholy"""
        from django.db.models import Sum
        total_points = self.questions.aggregate(total=Sum('points'))['total'] or 0
        return total_points if total_points > 0 else self.max_score

    def update_max_score(self):
        """تحديث الدرجة النهائية تلقائياً - zaki alkholy"""
        self.max_score = self.calculated_max_score
        self.save(update_fields=['max_score'])

    def is_passed(self, score):
        """تحديد النجاح = الدرجة >= نص الدرجة النهائية - zaki alkholy"""
        return score >= self.passing_score

    def __str__(self):
        return f"Quiz for {self.lesson.title} ({self.get_quiz_type_display()})"


# تحديث نموذج السؤال لإضافة دعم الصور وإزالة الأسئلة النصية - زكي الخولي
class Question(models.Model):
    QUESTION_TYPES = [
        ("multiple_choice", "Multiple Choice"),
        ("true_false", "True/False"),
        # تم إزالة 'short_answer' لأن المعلم يريد فقط اختيار من متعدد وصح/خطأ - زكي الخولي
    ]

    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name="questions")
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    text = models.TextField()
    # إضافة حقل الصورة للسؤال مع إمكانية أن يكون فارغ - زكي الخولي
    image = models.ImageField(
        upload_to="question_images/",
        blank=True,
        null=True,
        help_text="صورة اختيارية للسؤال",
    )
    points = models.PositiveIntegerField(default=1)
    order = models.PositiveIntegerField()

    class Meta:
        ordering = ["order"]

    def __str__(self):
        return f"Question: {self.text[:50]}..."


class Answer(models.Model):
    question = models.ForeignKey(
        Question, on_delete=models.CASCADE, related_name="answers"
    )
    text = models.TextField()
    is_correct = models.BooleanField(default=False)

    def __str__(self):
        return f"Answer: {self.text[:50]}..."


class UserQuizAttempt(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="quiz_attempts"
    )
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name="attempts")
    score = models.PositiveIntegerField(default=0)
    passed = models.BooleanField(default=False)
    answers = models.JSONField(default=dict, blank=True)  # تخزين إجابات الطالب
    submitted = models.BooleanField(default=False)  # هل تم تسليم الامتحان
    submitted_at = models.DateTimeField(null=True, blank=True)  # وقت التسليم
    created_at = models.DateTimeField(auto_now_add=True)  # وقت بدء المحاولة
    completed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Attempt by {self.user.username} on {self.quiz.title}"


class Announcement(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name="announcements"
    )
    title = models.CharField(max_length=200)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Announcement: {self.title} for {self.course.title}"


class FAQ(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name="faqs")
    question = models.CharField(max_length=200)
    answer = models.TextField()
    order = models.PositiveIntegerField(default=1)

    def __str__(self):
        return f"FAQ: {self.question} for {self.course.title}"


class InstructorAvailability(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="availabilities"
    )
    day = models.CharField(
        max_length=10,
        choices=[
            ("saturday", "Saturday"),
            ("sunday", "Sunday"),
            ("monday", "Monday"),
            ("tuesday", "Tuesday"),
            ("wednesday", "Wednesday"),
            ("thursday", "Thursday"),
            ("friday", "Friday"),
        ],
    )
    from_time = models.CharField(max_length=10)
    to_time = models.CharField(max_length=10)
    timezone = models.CharField(max_length=50, default="Africa/Cairo")
    enabled = models.BooleanField(default=True)
    note = models.CharField(max_length=255, default="بدون ملاحظات", blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.day}: {self.from_time} to {self.to_time} ({self.note})"


# إنشاء InstructorProfile تلقائياً عند إنشاء مستخدم معلم جديد
@receiver(post_save, sender=User)
def create_instructor_profile(sender, instance, created, **kwargs):
    if created and instance.is_instructor:
        from .models import InstructorProfile

        if not hasattr(instance, "instructor_profile"):
            InstructorProfile.objects.create(
                user=instance, specialization="", qualifications=""
            )


class Notification(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="notifications"
    )
    message = models.TextField()
    link = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    type = models.CharField(max_length=50, default="SYSTEM")

    def __str__(self):
        return f"{self.user.username} - {'Read' if self.is_read else 'Unread'}"


# =============================
# موديل InstructorPayout لتسجيل عمليات تحويل أرباح المعلمين zaki alkholy
# تم تعطيل هذا الموديل مؤقتاً حسب طلب المطور
# =============================
from django.conf import settings


class InstructorPayout(models.Model):
    PAYOUT_STATUS_CHOICES = [
        ("pending", "معلق"),
        ("processing", "قيد المعالجة"),
        ("completed", "مكتمل"),
        ("failed", "فاشل"),
    ]

    # ربط التحويل بمعلم محدد
    instructor = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="payouts"
    )
    # ربط التحويل بطلبات متعددة (ManyToMany)
    orders = models.ManyToManyField(
        "Order", related_name="payouts"
    )
    # إجمالي المبلغ المستحق للمعلم
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    # عمولة المنصة الإجمالية
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2)
    # صافي المبلغ المدفوع للمعلم بعد خصم عمولة المنصة
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2)
    # حالة التحويل
    status = models.CharField(
        max_length=20, choices=PAYOUT_STATUS_CHOICES, default="pending"
    )
    # وصل التحويل (صورة أو PDF)
    receipt = models.FileField(upload_to="payout_receipts/", blank=True, null=True)
    # ملاحظات إضافية
    notes = models.TextField(blank=True, null=True)
    # تاريخ إنشاء التحويل
    created_at = models.DateTimeField(auto_now_add=True)
    # تاريخ إتمام التحويل
    completed_at = models.DateTimeField(blank=True, null=True)
    # من قام بالتحويل (Admin)
    processed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="processed_payouts"
    )

    def __str__(self):
        return f"Payout for {self.instructor.username} - {self.amount_paid} EGP"

    class Meta:
        ordering = ["-created_at"]

# =============================
# نهاية موديل تحويل الأموال للمعلم - zaki alkholy
# =============================


# =============================
# نماذج تتبع التقدم والنقاط - zaki alkholy
# =============================

class StudentProgress(models.Model):
    """نموذج لتتبع تقدم الطالب في كل درس"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="lesson_progress"
    )
    lesson = models.ForeignKey(
        Lesson, on_delete=models.CASCADE, related_name="student_progress"
    )
    course = models.ForeignKey(
        Course, on_delete=models.CASCADE, related_name="student_progress"
    )

    # حالة الدرس
    STATUS_CHOICES = [
        ("not_started", "لم يبدأ"),
        ("in_progress", "جاري المشاهدة"),
        ("completed", "مكتمل"),
        ("skipped", "تم التخطي"),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="not_started")

    # تفاصيل المشاهدة
    watch_time = models.PositiveIntegerField(default=0, help_text="الوقت المشاهد بالثواني")
    total_duration = models.PositiveIntegerField(default=0, help_text="المدة الإجمالية بالثواني")
    completion_percentage = models.PositiveIntegerField(
        default=0, validators=[MinValueValidator(0), MaxValueValidator(100)]
    )

    # التواريخ
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_accessed = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # عدد مرات المشاهدة
    view_count = models.PositiveIntegerField(default=0)

    # تتبع جلسات المشاهدة - zaki alkholy
    last_session_start = models.DateTimeField(null=True, blank=True, help_text="بداية آخر جلسة مشاهدة")
    session_timeout_minutes = models.PositiveIntegerField(default=30, help_text="انتهاء الجلسة بعد دقائق من عدم النشاط")

    class Meta:
        unique_together = ("student", "lesson")
        ordering = ["course", "lesson__order"]

    def __str__(self):
        return f"{self.student.username} - {self.lesson.title} ({self.get_status_display()})"


class StudentPoints(models.Model):
    """نموذج لتتبع نقاط الطالب"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="points_profile"
    )

    # النقاط
    total_points = models.PositiveIntegerField(default=0)
    available_points = models.PositiveIntegerField(default=0)  # النقاط المتاحة للاستخدام
    spent_points = models.PositiveIntegerField(default=0)  # النقاط المستخدمة

    # المستوى
    LEVEL_CHOICES = [
        ("bronze", "البرونزي"),
        ("silver", "الفضي"),
        ("gold", "الذهبي"),
        ("platinum", "البلاتيني"),
        ("diamond", "الماسي"),
    ]
    current_level = models.CharField(max_length=20, choices=LEVEL_CHOICES, default="bronze")

    # إحصائيات
    lessons_completed = models.PositiveIntegerField(default=0)
    quizzes_passed = models.PositiveIntegerField(default=0)
    courses_completed = models.PositiveIntegerField(default=0)
    login_streak = models.PositiveIntegerField(default=0)  # أيام الدخول المتتالية
    last_login_date = models.DateField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.student.username} - {self.total_points} نقطة ({self.get_current_level_display()})"

    def add_points(self, points, reason=""):
        """إضافة نقاط للطالب"""
        self.total_points += points
        self.available_points += points
        self.save()

        # إنشاء سجل للنقاط
        PointsHistory.objects.create(
            student=self.student,
            points=points,
            action="earned",
            reason=reason
        )

        # تحديث المستوى
        self.update_level()

    def spend_points(self, points, reason=""):
        """استخدام النقاط"""
        if self.available_points >= points:
            self.available_points -= points
            self.spent_points += points
            self.save()

            # إنشاء سجل للنقاط
            PointsHistory.objects.create(
                student=self.student,
                points=-points,
                action="spent",
                reason=reason
            )
            return True
        return False

    def update_level(self):
        """تحديث مستوى الطالب بناءً على النقاط"""
        if self.total_points >= 2000:
            self.current_level = "diamond"
        elif self.total_points >= 1000:
            self.current_level = "platinum"
        elif self.total_points >= 500:
            self.current_level = "gold"
        elif self.total_points >= 200:
            self.current_level = "silver"
        else:
            self.current_level = "bronze"
        self.save()


class PointsHistory(models.Model):
    """سجل تاريخ النقاط"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="points_history"
    )
    points = models.IntegerField()  # يمكن أن يكون سالب للنقاط المستخدمة

    ACTION_CHOICES = [
        ("earned", "مكتسبة"),
        ("spent", "مستخدمة"),
        ("bonus", "مكافأة"),
        ("penalty", "خصم"),
    ]
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    reason = models.CharField(max_length=200)

    # ربط بالنشاط
    lesson = models.ForeignKey(Lesson, on_delete=models.SET_NULL, null=True, blank=True)
    quiz = models.ForeignKey(Quiz, on_delete=models.SET_NULL, null=True, blank=True)
    course = models.ForeignKey(Course, on_delete=models.SET_NULL, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.student.username} - {self.points} نقطة ({self.get_action_display()})"


class Achievement(models.Model):
    """نموذج الإنجازات والميداليات"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # معلومات الإنجاز
    name = models.CharField(max_length=100)
    description = models.TextField()
    icon = models.CharField(max_length=50, default="🏆")  # إيموجي أو اسم أيقونة

    # شروط الحصول على الإنجاز
    ACHIEVEMENT_TYPE_CHOICES = [
        ("lessons_completed", "إكمال دروس"),
        ("quizzes_passed", "نجاح في اختبارات"),
        ("courses_completed", "إكمال دورات"),
        ("login_streak", "دخول متتالي"),
        ("points_earned", "كسب نقاط"),
        ("perfect_score", "درجة كاملة"),
        ("fast_completion", "إكمال سريع"),
        ("first_course", "أول دورة"),
        ("review_given", "كتابة تقييم"),
    ]
    achievement_type = models.CharField(max_length=30, choices=ACHIEVEMENT_TYPE_CHOICES)
    required_count = models.PositiveIntegerField(default=1)  # العدد المطلوب

    # المكافآت
    points_reward = models.PositiveIntegerField(default=0)

    # الحالة
    is_active = models.BooleanField(default=True)
    is_hidden = models.BooleanField(default=False)  # إنجازات مخفية

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["achievement_type", "required_count"]

    def __str__(self):
        return f"{self.name} ({self.get_achievement_type_display()})"


class StudentAchievement(models.Model):
    """إنجازات الطالب المحققة"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="achievements"
    )
    achievement = models.ForeignKey(
        Achievement, on_delete=models.CASCADE, related_name="student_achievements"
    )

    # تفاصيل الحصول على الإنجاز
    earned_at = models.DateTimeField(auto_now_add=True)
    progress_when_earned = models.PositiveIntegerField(default=0)  # التقدم عند الحصول عليه

    # ربط بالنشاط المرتبط
    related_course = models.ForeignKey(Course, on_delete=models.SET_NULL, null=True, blank=True)
    related_lesson = models.ForeignKey(Lesson, on_delete=models.SET_NULL, null=True, blank=True)
    related_quiz = models.ForeignKey(Quiz, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        unique_together = ("student", "achievement")
        ordering = ["-earned_at"]

    def __str__(self):
        return f"{self.student.username} - {self.achievement.name}"


class Assignment(models.Model):
    """نموذج الواجبات التفاعلية"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name="assignments")

    # معلومات الواجب
    title = models.CharField(max_length=200)
    description = models.TextField()
    instructions = models.TextField(help_text="تعليمات تفصيلية للطالب")

    # نوع الواجب
    ASSIGNMENT_TYPE_CHOICES = [
        ("file_upload", "رفع ملف"),
        ("text_submission", "إرسال نص"),
        ("project", "مشروع"),
        ("presentation", "عرض تقديمي"),
        ("code_submission", "كود برمجي"),
        ("design", "تصميم"),
    ]
    assignment_type = models.CharField(max_length=30, choices=ASSIGNMENT_TYPE_CHOICES)

    # الدرجات والتقييم
    max_score = models.PositiveIntegerField(default=100)
    passing_score = models.PositiveIntegerField(default=70)

    # المواعيد
    due_date = models.DateTimeField()
    late_submission_allowed = models.BooleanField(default=True)
    late_penalty_percentage = models.PositiveIntegerField(default=10)  # خصم التأخير

    # الملفات المطلوبة
    allowed_file_types = models.CharField(
        max_length=200,
        default="pdf,doc,docx,txt,zip",
        help_text="أنواع الملفات المسموحة مفصولة بفاصلة"
    )
    max_file_size_mb = models.PositiveIntegerField(default=10)

    # الحالة
    is_published = models.BooleanField(default=False)
    auto_grade = models.BooleanField(default=False)  # تقييم تلقائي

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["due_date"]

    def __str__(self):
        return f"{self.lesson.course.title} - {self.title}"

    def is_overdue(self):
        """التحقق من انتهاء موعد التسليم"""
        return timezone.now() > self.due_date


class AssignmentSubmission(models.Model):
    """تسليم الواجبات من الطلاب"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    assignment = models.ForeignKey(
        Assignment, on_delete=models.CASCADE, related_name="submissions"
    )
    student = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="assignment_submissions"
    )

    # المحتوى المرسل
    text_content = models.TextField(blank=True)
    file_upload = models.FileField(upload_to="assignment_submissions/", blank=True, null=True)
    submission_notes = models.TextField(blank=True, help_text="ملاحظات الطالب")

    # التقييم
    score = models.PositiveIntegerField(null=True, blank=True)
    feedback = models.TextField(blank=True, help_text="تعليقات المعلم")

    STATUS_CHOICES = [
        ("draft", "مسودة"),
        ("submitted", "مرسل"),
        ("graded", "مقيم"),
        ("returned", "مُعاد"),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="draft")

    # التواريخ
    submitted_at = models.DateTimeField(null=True, blank=True)
    graded_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # معلومات إضافية
    is_late = models.BooleanField(default=False)
    attempt_number = models.PositiveIntegerField(default=1)

    class Meta:
        unique_together = ("assignment", "student", "attempt_number")
        ordering = ["-submitted_at"]

    def __str__(self):
        return f"{self.student.username} - {self.assignment.title} (محاولة {self.attempt_number})"

    def calculate_final_score(self):
        """حساب الدرجة النهائية مع خصم التأخير"""
        if not self.score:
            return 0

        if self.is_late and self.assignment.late_penalty_percentage > 0:
            penalty = (self.score * self.assignment.late_penalty_percentage) / 100
            return max(0, self.score - penalty)

        return self.score


class ReviewSchedule(models.Model):
    """نموذج جدولة المراجعة المتباعدة"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    student = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="review_schedules"
    )

    # المحتوى المراد مراجعته
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, related_name="review_schedules")
    quiz = models.ForeignKey(Quiz, on_delete=models.SET_NULL, null=True, blank=True, related_name="review_schedules")

    # معلومات المراجعة
    content_type = models.CharField(
        max_length=20,
        choices=[
            ("lesson", "درس"),
            ("quiz", "اختبار"),
            ("vocabulary", "مفردات"),
            ("concept", "مفهوم"),
        ],
        default="lesson"
    )
    content_title = models.CharField(max_length=200)
    content_summary = models.TextField(blank=True)

    # جدولة المراجعة
    initial_learned_date = models.DateTimeField()  # تاريخ التعلم الأول
    next_review_date = models.DateTimeField()  # موعد المراجعة القادم
    review_interval_days = models.PositiveIntegerField(default=1)  # الفترة بين المراجعات

    # إحصائيات الأداء
    review_count = models.PositiveIntegerField(default=0)  # عدد مرات المراجعة
    success_count = models.PositiveIntegerField(default=0)  # عدد المراجعات الناجحة
    difficulty_level = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="مستوى الصعوبة من 1 إلى 5"
    )

    # الحالة
    is_active = models.BooleanField(default=True)
    is_mastered = models.BooleanField(default=False)  # تم إتقانه

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["next_review_date"]
        unique_together = ("student", "lesson", "content_type")

    def __str__(self):
        return f"{self.student.username} - {self.content_title} (مراجعة في {self.next_review_date.date()})"

    def update_schedule(self, success=True):
        """تحديث جدولة المراجعة بناءً على الأداء"""
        self.review_count += 1

        if success:
            self.success_count += 1
            # زيادة الفترة عند النجاح
            if self.difficulty_level <= 2:
                self.review_interval_days *= 2
            else:
                self.review_interval_days = int(self.review_interval_days * 1.5)

            # تقليل مستوى الصعوبة
            if self.difficulty_level > 1:
                self.difficulty_level -= 1
        else:
            # تقليل الفترة عند الفشل
            self.review_interval_days = max(1, self.review_interval_days // 2)

            # زيادة مستوى الصعوبة
            if self.difficulty_level < 5:
                self.difficulty_level += 1

        # تحديد موعد المراجعة القادم
        self.next_review_date = timezone.now() + timezone.timedelta(days=self.review_interval_days)

        # التحقق من الإتقان (5 مراجعات ناجحة متتالية مع صعوبة منخفضة)
        if self.success_count >= 5 and self.difficulty_level == 1:
            self.is_mastered = True
            self.is_active = False

        self.save()


class LessonAnalytics(models.Model):
    """تحليلات تفصيلية للدروس"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    lesson = models.OneToOneField(
        Lesson, on_delete=models.CASCADE, related_name="analytics"
    )

    # إحصائيات المشاهدة
    total_views = models.PositiveIntegerField(default=0)
    unique_viewers = models.PositiveIntegerField(default=0)
    average_watch_time = models.PositiveIntegerField(default=0)  # بالثواني
    completion_rate = models.FloatField(default=0.0)  # نسبة الإكمال

    # نقاط التوقف الشائعة
    common_drop_points = models.JSONField(default=list, blank=True)  # نقاط التوقف بالثواني
    replay_segments = models.JSONField(default=list, blank=True)  # الأجزاء المُعادة

    # تقييمات الطلاب
    average_rating = models.FloatField(default=0.0)
    difficulty_rating = models.FloatField(default=0.0)  # تقييم الصعوبة

    # إحصائيات زمنية
    peak_viewing_hours = models.JSONField(default=list, blank=True)  # ساعات الذروة

    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"تحليلات {self.lesson.title}"


# =============================
# إشارات لتحديث النقاط والإنجازات تلقائياً - zaki alkholy
# =============================

@receiver(post_save, sender=User)
def create_student_points_profile(sender, instance, created, **kwargs):
    """إنشاء ملف نقاط للطالب الجديد"""
    if created and instance.is_student:
        StudentPoints.objects.get_or_create(student=instance)


@receiver(post_save, sender=StudentProgress)
def update_points_on_lesson_completion(sender, instance, created, **kwargs):
    """إضافة نقاط عند إكمال درس"""
    if instance.status == "completed" and instance.completion_percentage >= 80:
        points_profile, _ = StudentPoints.objects.get_or_create(student=instance.student)

        # إضافة نقاط لإكمال الدرس
        points_profile.add_points(10, f"إكمال درس: {instance.lesson.title}")
        points_profile.lessons_completed += 1
        points_profile.save()

        # التحقق من الإنجازات
        check_achievements(instance.student, "lessons_completed", points_profile.lessons_completed)


@receiver(post_save, sender=UserQuizAttempt)
def update_points_on_quiz_completion(sender, instance, created, **kwargs):
    """إضافة نقاط عند النجاح في اختبار"""
    if instance.passed and instance.submitted:
        points_profile, _ = StudentPoints.objects.get_or_create(student=instance.user)

        # نقاط أساسية للنجاح
        base_points = 20

        # نقاط إضافية للدرجة العالية
        if instance.score >= 90:
            bonus_points = 10
            points_profile.add_points(base_points + bonus_points, f"نجاح ممتاز في: {instance.quiz.title}")
        else:
            points_profile.add_points(base_points, f"نجاح في: {instance.quiz.title}")

        points_profile.quizzes_passed += 1
        points_profile.save()

        # التحقق من الإنجازات
        check_achievements(instance.user, "quizzes_passed", points_profile.quizzes_passed)
        if instance.score == 100:
            check_achievements(instance.user, "perfect_score", 1)


def check_achievements(student, achievement_type, current_count):
    """التحقق من الإنجازات وإضافتها للطالب"""
    achievements = Achievement.objects.filter(
        achievement_type=achievement_type,
        required_count__lte=current_count,
        is_active=True
    ).exclude(
        student_achievements__student=student
    )

    for achievement in achievements:
        StudentAchievement.objects.create(
            student=student,
            achievement=achievement,
            progress_when_earned=current_count
        )

        # إضافة نقاط المكافأة
        if achievement.points_reward > 0:
            points_profile, _ = StudentPoints.objects.get_or_create(student=student)
            points_profile.add_points(
                achievement.points_reward,
                f"إنجاز: {achievement.name}"
            )


# =============================
# نموذج سجل مراجعة الإدارة - zaki alkholy
# =============================

class AdminAuditLog(models.Model):
    """سجل مراجعة شامل لجميع أعمال الإدارة"""

    ACTION_TYPES = (
        # Dashboard Actions
        ('dashboard_login', 'تسجيل دخول Dashboard'),
        ('dashboard_logout', 'تسجيل خروج Dashboard'),
        ('dashboard_view', 'عرض Dashboard'),

        # Payment Actions
        ('payout_created', 'إنشاء تحويل'),
        ('payout_processed', 'معالجة تحويل'),
        ('payout_completed', 'إكمال تحويل'),
        ('payout_cancelled', 'إلغاء تحويل'),
        ('receipt_uploaded', 'رفع وصل تحويل'),

        # User Management
        ('user_created', 'إنشاء مستخدم'),
        ('user_updated', 'تحديث مستخدم'),
        ('user_deleted', 'حذف مستخدم'),
        ('user_activated', 'تفعيل مستخدم'),
        ('user_deactivated', 'إلغاء تفعيل مستخدم'),

        # Course Management
        ('course_approved', 'الموافقة على كورس'),
        ('course_rejected', 'رفض كورس'),
        ('course_suspended', 'تعليق كورس'),
        ('course_deleted', 'حذف كورس'),

        # Order Management
        ('order_refunded', 'استرداد طلب'),
        ('order_cancelled', 'إلغاء طلب'),

        # System Actions
        ('settings_changed', 'تغيير إعدادات'),
        ('backup_created', 'إنشاء نسخة احتياطية'),
        ('data_exported', 'تصدير بيانات'),

        # Security Actions
        ('permission_granted', 'منح صلاحية'),
        ('permission_revoked', 'سحب صلاحية'),
        ('security_alert', 'تنبيه أمني'),
    )

    SEVERITY_LEVELS = (
        ('low', 'منخفض'),
        ('medium', 'متوسط'),
        ('high', 'عالي'),
        ('critical', 'حرج'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # من قام بالعمل
    admin_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='admin_actions')
    admin_username = models.CharField(max_length=150)  # backup في حالة حذف المستخدم
    admin_email = models.EmailField(blank=True, null=True)

    # تفاصيل العمل
    action_type = models.CharField(max_length=50, choices=ACTION_TYPES)
    action_description = models.TextField()
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS, default='medium')

    # الكائن المتأثر
    target_model = models.CharField(max_length=100, blank=True, null=True)  # اسم الـ model
    target_object_id = models.CharField(max_length=100, blank=True, null=True)  # ID الكائن
    target_object_repr = models.CharField(max_length=200, blank=True, null=True)  # وصف الكائن

    # البيانات
    old_values = models.JSONField(blank=True, null=True)  # القيم القديمة
    new_values = models.JSONField(blank=True, null=True)  # القيم الجديدة
    additional_data = models.JSONField(blank=True, null=True)  # بيانات إضافية

    # معلومات تقنية
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)
    session_key = models.CharField(max_length=40, blank=True, null=True)

    # التوقيت
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'سجل مراجعة الإدارة'
        verbose_name_plural = 'سجلات مراجعة الإدارة'
        indexes = [
            models.Index(fields=['admin_user', 'timestamp']),
            models.Index(fields=['action_type', 'timestamp']),
            models.Index(fields=['target_model', 'target_object_id']),
            models.Index(fields=['severity', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.admin_username} - {self.get_action_type_display()} - {self.timestamp}"

    @property
    def is_critical(self):
        return self.severity == 'critical'

    @property
    def is_security_related(self):
        security_actions = [
            'permission_granted', 'permission_revoked', 'security_alert',
            'user_activated', 'user_deactivated'
        ]
        return self.action_type in security_actions
