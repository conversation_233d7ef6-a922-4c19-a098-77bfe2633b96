# Django management command لفحص الباسوردات الضعيفة - <PERSON><PERSON>
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import check_password
import secrets
import string

User = get_user_model()

class Command(BaseCommand):
    help = 'فحص وتحديث الباسوردات الضعيفة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='تحديث الباسوردات الضعيفة تلقائياً',
        )

    def handle(self, *args, **options):
        # قائمة الباسوردات الضعيفة الشائعة
        weak_passwords = [
            '123456', 'password', 'admin', 'qwerty', '12345678',
            'abc123', 'password123', '123456789', 'welcome',
            'login', 'guest', 'test', 'user', '1234', '12345'
        ]
        
        self.stdout.write("🔍 فحص الباسوردات الضعيفة...")
        
        weak_users = []
        
        for user in User.objects.all():
            for weak_pass in weak_passwords:
                if check_password(weak_pass, user.password):
                    weak_users.append((user, weak_pass))
                    break
        
        if not weak_users:
            self.stdout.write(
                self.style.SUCCESS("✅ لا توجد باسوردات ضعيفة!")
            )
            return
        
        self.stdout.write(
            self.style.WARNING(f"⚠️  تم العثور على {len(weak_users)} مستخدم بباسورد ضعيف:")
        )
        
        for user, weak_pass in weak_users:
            self.stdout.write(f"  - {user.username}: {weak_pass}")
        
        if options['fix']:
            self.stdout.write("\n🔧 تحديث الباسوردات...")
            
            for user, weak_pass in weak_users:
                # إنشاء باسورد قوي جديد
                new_password = self.generate_strong_password()
                user.set_password(new_password)
                user.save()
                
                self.stdout.write(
                    f"✅ تم تحديث باسورد {user.username}: {new_password}"
                )
            
            self.stdout.write(
                self.style.SUCCESS(f"\n🎉 تم تحديث {len(weak_users)} باسورد بنجاح!")
            )
        else:
            self.stdout.write(
                self.style.WARNING("\n💡 لتحديث الباسوردات تلقائياً، استخدم: --fix")
            )
    
    def generate_strong_password(self, length=12):
        """إنشاء باسورد قوي"""
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(characters) for _ in range(length))
