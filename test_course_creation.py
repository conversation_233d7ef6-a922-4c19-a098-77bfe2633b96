#!/usr/bin/env python3
"""
Simple test script to debug course creation
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Newmnasa.settings')
django.setup()

from django.contrib.auth import get_user_model
from main.models import Course, Category, MainCategory
from main.serializers_file.course_serializer import CourseWriteSerializer
from rest_framework.test import APIRequestFactory
from rest_framework.request import Request

User = get_user_model()

def test_course_creation():
    """Test course creation scenarios"""
    print("🔍 Testing Course Creation...")
    print("=" * 50)
    
    # Test 1: Check if we have users
    print("\n1. Checking users:")
    users = User.objects.all()
    print(f"   Total users: {users.count()}")
    
    instructors = User.objects.filter(is_instructor=True)
    print(f"   Instructors: {instructors.count()}")
    
    if instructors.exists():
        instructor = instructors.first()
        print(f"   First instructor: {instructor.username}")
        print(f"   Is instructor: {instructor.is_instructor}")
        print(f"   Wallet number: {instructor.wallet_number}")
        print(f"   Payment method: {instructor.payment_method}")
        print(f"   Has wallet: {instructor.has_wallet}")
    else:
        print("   ❌ No instructors found!")
        return
    
    # Test 2: Check categories
    print("\n2. Checking categories:")
    categories = Category.objects.all()
    print(f"   Total categories: {categories.count()}")
    
    if categories.exists():
        category = categories.first()
        print(f"   First category: {category.name} (ID: {category.id})")
    else:
        print("   ❌ No categories found!")
        # Create a test category
        main_cat = MainCategory.objects.first()
        if main_cat:
            category = Category.objects.create(
                name="Test Category",
                slug="test-category",
                main_category=main_cat
            )
            print(f"   ✅ Created test category: {category.name}")
        else:
            print("   ❌ No main categories found either!")
            return
    
    # Test 3: Test serializer validation
    print("\n3. Testing serializer validation:")
    
    # Test data without wallet/payment info
    test_data = {
        'title': 'Test Course',
        'description': 'Test course description',
        'price': 150.00,
        'category': category.id,
        'level': 'beginner',
        'language': 'Arabic',
        'currency': 'EGP'
    }
    
    print(f"   Test data: {test_data}")
    
    # Create a mock request
    factory = APIRequestFactory()
    request = factory.post('/api/courses/', test_data)
    request.user = instructor
    
    serializer = CourseWriteSerializer(data=test_data)
    print(f"   Serializer is valid: {serializer.is_valid()}")
    
    if not serializer.is_valid():
        print(f"   ❌ Serializer errors: {serializer.errors}")
    else:
        print("   ✅ Serializer validation passed")
        
        # Test 4: Check wallet/payment validation
        print("\n4. Testing wallet/payment validation:")
        if not instructor.wallet_number or not instructor.payment_method:
            print("   ❌ Instructor missing wallet_number or payment_method")
            print(f"      wallet_number: {instructor.wallet_number}")
            print(f"      payment_method: {instructor.payment_method}")
            
            # Update instructor with wallet info
            instructor.wallet_number = "01234567890"
            instructor.payment_method = "vodafone_cash"
            instructor.save()
            print("   ✅ Updated instructor with wallet info")
        else:
            print("   ✅ Instructor has wallet and payment method")
        
        # Test 5: Try to save the course
        print("\n5. Testing course creation:")
        try:
            course = serializer.save(instructor=instructor)
            print(f"   ✅ Course created successfully: {course.title}")
            print(f"      Course ID: {course.id}")
            print(f"      Course slug: {course.slug}")
        except Exception as e:
            print(f"   ❌ Course creation failed: {e}")
    
    # Test 6: Check required fields
    print("\n6. Checking required fields:")
    required_fields = ['title', 'description', 'price']
    for field in required_fields:
        if field not in test_data:
            print(f"   ❌ Missing required field: {field}")
        else:
            print(f"   ✅ Has required field: {field}")

def main():
    """Main function"""
    print("🚀 Course Creation Debug Tool")
    print("=" * 50)
    
    try:
        test_course_creation()
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("✅ Debug complete!")

if __name__ == "__main__":
    main()
