# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.views import APIView
from rest_framework.response import Response

# Django authentication
from rest_framework.permissions import IsAuthenticated

# Django models and aggregation - zaki alkholy
from django.db import models

# Models
from ..models import (
    InstructorProfile,
    InstructorAvailability,
    User,
    Order,
    Course,
    Enrollment,
    StudentProgress,
    InstructorPayout,
    StudentPoints,
    UserQuizAttempt,
)

# Serializers
from ..serializers_file.instructor_serializer import (
    InstructorProfileSerializer,
    InstructorAvailabilitySerializer,
    InstructorWithCoursesSerializer,
)

# Django ORM tools
from django.db.models import Sum, Count, Avg, Q, F
from django.utils import timezone
from datetime import timedelta

# Python built-in
import datetime
import traceback


class InstructorProfileViewSet(viewsets.ModelViewSet):
    queryset = InstructorProfile.objects.all()
    serializer_class = InstructorProfileSerializer
    permission_classes = [IsAuthenticated]
    lookup_field = 'id'  # استخدام ID لملفات المعلمين - z<PERSON> al<PERSON><PERSON>

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)


class InstructorAvailabilityViewSet(viewsets.ModelViewSet):
    queryset = InstructorAvailability.objects.all()
    serializer_class = InstructorAvailabilitySerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID لمواعيد المعلمين - zaki alkholy

    def get_queryset(self):
        user = self.request.user
        user_id = self.request.query_params.get("user_id")
        date_param = self.request.query_params.get("date")
        period = self.request.query_params.get("period")  # new

        queryset = self.queryset

        if user.is_staff and user_id:
            queryset = queryset.filter(user_id=user_id)
        else:
            queryset = queryset.filter(user=user)

        if date_param:
            import datetime

            try:
                date_obj = datetime.datetime.strptime(date_param, "%Y-%m-%d")
                day_name = date_obj.strftime("%A").lower()
                queryset = queryset.filter(day=day_name)
            except:
                pass

        if period == "am":
            queryset = queryset.filter(from_time__lt="12:00")
        elif period == "pm":
            queryset = queryset.filter(from_time__gte="12:00")

        return queryset

    def perform_create(self, serializer):
        user = self.request.user
        user_id = self.request.data.get("user_id")
        # الأدمن يقدر يحدد user_id، المدرس لا
        if user.is_staff and user_id:
            serializer.save(user_id=user_id)
        else:
            serializer.save(user=user)


class InstructorDetailAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        try:
            instructor = User.objects.get(id=id, is_instructor=True)
            data = InstructorWithCoursesSerializer(
                instructor, context={"request": request}
            ).data
            return Response(data)
        except User.DoesNotExist:
            return Response({"detail": "Instructor not found."}, status=404)
        except Exception as e:
            return Response({"detail": str(e)}, status=500)


class InstructorDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        total_courses = user.taught_courses.count()
        active_courses = user.taught_courses.filter(is_published=True).count()
        student_ids = set()
        for course in user.taught_courses.all():
            student_ids.update(course.students.values_list("id", flat=True))
        total_students = len(student_ids)

        # حساب إجمالي الإيرادات (95% من سعر الكورس - أرباح المعلم فقط)
        completed_orders = Order.objects.filter(course__instructor=user, status="completed")
        total_instructor_revenue = 0
        total_platform_fee = 0
        total_gross_revenue = 0

        for order in completed_orders:
            base_price = float(order.base_price) if order.base_price else float(order.amount)
            platform_fee = float(order.platform_fee) if order.platform_fee else round(base_price * 0.05, 2)
            instructor_earnings = base_price - platform_fee

            total_gross_revenue += base_price
            total_platform_fee += platform_fee
            total_instructor_revenue += instructor_earnings

        # إحصائيات التحويلات
        completed_payouts = InstructorPayout.objects.filter(
            instructor=user,
            status='completed'
        ).count()

        pending_payouts = InstructorPayout.objects.filter(
            instructor=user,
            status='pending'
        ).count()

        total_payouts = completed_payouts + pending_payouts

        return Response(
            {
                "total_courses": total_courses,
                "active_courses": active_courses,
                "total_students": total_students,
                "total_revenue": round(total_instructor_revenue, 2),  # أرباح المعلم فقط (95%)
                "gross_revenue": round(total_gross_revenue, 2),  # إجمالي المبيعات (100%)
                "platform_fee": round(total_platform_fee, 2),  # عمولة المنصة (5%)
                "payouts_stats": {
                    "completed_payouts": completed_payouts,
                    "pending_payouts": pending_payouts,
                    "total_payouts": total_payouts,
                    "completion_rate": round((completed_payouts / total_payouts * 100) if total_payouts > 0 else 0, 1)
                }
            }
        )


class InstructorStudentsAPIView(APIView):
    """API لجلب بيانات الطلاب للمعلم مع تفاصيل التقدم - zaki alkholy"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        # التحقق من أن المستخدم معلم
        if not user.is_instructor:
            return Response(
                {"error": "غير مصرح لك بالوصول لهذه البيانات"},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            # جلب جميع كورسات المعلم
            instructor_courses = Course.objects.filter(instructor=user)

            if not instructor_courses.exists():
                return Response({
                    'students': [],
                    'stats': {
                        'total_students': 0,
                        'active_students': 0,
                        'average_progress': 0,
                        'completion_rate': 0
                    },
                    'courses': []
                })

            # جلب جميع التسجيلات في كورسات المعلم (استبعاد المعلم نفسه)
            enrollments = Enrollment.objects.filter(
                course__in=instructor_courses
            ).exclude(student=user).select_related('student', 'course')

            students_data = []
            students_stats = {
                'total_students': 0,
                'active_students': 0,
                'average_progress': 0,
                'completion_rate': 0
            }

            # معالجة كل تسجيل
            for enrollment in enrollments:
                try:
                    student = enrollment.student
                    course = enrollment.course

                    # جلب تقدم الطالب في هذا الكورس
                    progress_records = StudentProgress.objects.filter(
                        student=student,
                        course=course
                    )

                    # حساب إحصائيات التقدم
                    total_lessons = course.lessons.count()
                    completed_lessons = progress_records.filter(
                        completion_percentage__gte=80
                    ).count()

                    # حساب متوسط التقدم
                    avg_progress = progress_records.aggregate(
                        avg=Avg('completion_percentage')
                    )['avg'] or 0

                    # حساب إجمالي وقت المشاهدة
                    total_watch_time = progress_records.aggregate(
                        total=Sum('watch_time')
                    )['total'] or 0

                    # جلب درجات الامتحانات والواجبات - zaki alkholy
                    quiz_attempts = UserQuizAttempt.objects.filter(
                        user=student,
                        quiz__lesson__course=course
                    )

                    # فصل الامتحانات عن الواجبات - zaki alkholy
                    exam_scores = []  # امتحانات
                    assignment_scores = []  # واجبات
                    total_actual_score = 0  # مجموع الدرجات الفعلية - zaki alkholy
                    total_max_score = 0     # مجموع الدرجات النهائية - zaki alkholy
                    total_percentage_score = 0  # مجموع النسب المئوية - zaki alkholy
                    passed_quizzes = 0

                    for attempt in quiz_attempts:
                        # تحديد نوع الاختبار حسب quiz_type - zaki alkholy
                        quiz_type = getattr(attempt.quiz, 'quiz_type', 'exam')

                        # النظام الجديد البسيط - zaki alkholy
                        max_score = attempt.quiz.calculated_max_score
                        passing_score = max_score / 2  # نص الدرجة النهائية

                        # حساب النسبة المئوية الصحيحة - zaki alkholy
                        percentage = (attempt.score / max_score * 100) if max_score > 0 else 0

                        quiz_data = {
                            'quiz_title': attempt.quiz.title,
                            'score': attempt.score,
                            'max_score': max_score,
                            'passing_score': passing_score,
                            'percentage': percentage,
                            'passed': attempt.score >= passing_score,  # النجاح = الدرجة >= نص الدرجة النهائية - zaki alkholy
                            'completed_at': attempt.completed_at,
                            'quiz_type': quiz_type
                        }

                        # تصنيف حسب النوع - zaki alkholy
                        if quiz_type == 'assignment':
                            assignment_scores.append(quiz_data)
                        else:
                            exam_scores.append(quiz_data)

                        # حساب المجاميع - zaki alkholy
                        total_actual_score += attempt.score
                        total_max_score += max_score
                        total_percentage_score += percentage
                        # التحقق من النجاح - zaki alkholy
                        if quiz_data['passed']:
                            passed_quizzes += 1

                    # دمج الامتحانات والواجبات للحفاظ على التوافق - zaki alkholy
                    all_quiz_scores = exam_scores + assignment_scores
                    avg_quiz_score = total_percentage_score / len(all_quiz_scores) if all_quiz_scores else 0

                    # جلب نقاط الطالب
                    student_points = StudentPoints.objects.filter(student=student).first()

                    # تجميع بيانات الطالب - zaki alkholy
                    student_data = {
                        'id': str(student.id),
                        'username': student.username,
                        'email': student.email,
                        'name': f"{student.first_name} {student.last_name}".strip() or student.username,
                        'phone_number': student.phone_number or 'غير متوفر',  # إضافة رقم الهاتف - zaki alkholy
                        'profile_image': student.profile_image.url if student.profile_image else None,
                        'enrolled_at': enrollment.enrolled_at,
                        'last_login': student.last_login,

                        # بيانات الكورس
                        'course': {
                            'id': str(course.id),
                            'title': course.title
                        },

                        # إحصائيات التقدم
                        'progress': round(avg_progress),
                        'completed_lessons': completed_lessons,
                        'total_lessons': total_lessons,
                        'total_watch_time': round(total_watch_time / 60),  # بالدقائق

                        # إحصائيات الامتحانات والواجبات - zaki alkholy
                        'quiz_scores': all_quiz_scores,  # كل الاختبارات
                        'exam_scores': exam_scores,      # الامتحانات فقط
                        'assignment_scores': assignment_scores,  # الواجبات فقط
                        'average_quiz_score': round(avg_quiz_score),
                        'passed_quizzes': passed_quizzes,
                        'total_quizzes': len(all_quiz_scores),
                        'total_exams': len(exam_scores),
                        'total_assignments': len(assignment_scores),

                        # مجاميع الدرجات الفعلية - zaki alkholy
                        'total_actual_score': total_actual_score,
                        'total_max_score': total_max_score,

                        # النقاط والمستوى
                        'total_points': student_points.total_points if student_points else 0,
                        'current_level': student_points.current_level if student_points else 'bronze',

                        # النشاط
                        'last_activity': student.last_login.strftime('%Y-%m-%d') if student.last_login else 'لم يسجل دخول',
                        'is_active': student.last_login and (timezone.now() - student.last_login).days <= 7 if student.last_login else False
                    }

                    students_data.append(student_data)

                except Exception as e:
                    # تسجيل الخطأ ومتابعة مع الطالب التالي
                    continue

            # حساب الإحصائيات العامة
            if students_data:
                students_stats['total_students'] = len(students_data)
                students_stats['active_students'] = sum(1 for s in students_data if s['is_active'])
                students_stats['average_progress'] = round(
                    sum(s['progress'] for s in students_data) / len(students_data)
                )
                students_stats['completion_rate'] = round(
                    sum(1 for s in students_data if s['progress'] >= 80) / len(students_data) * 100
                )

            return Response({
                'students': students_data,
                'stats': students_stats,
                'courses': [
                    {'id': str(c.id), 'title': c.title}
                    for c in instructor_courses
                ]
            })

        except Exception as e:
            return Response(
                {"error": f"خطأ في جلب بيانات الطلاب: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InstructorPayoutsAPIView(APIView):
    """عرض التحويلات المكتملة للمعلم المسجل دخوله"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # التأكد من أن المستخدم معلم
            if not user.is_instructor:
                return Response(
                    {"error": "هذا الـ API مخصص للمعلمين فقط"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # جلب التحويلات المكتملة فقط (اللي فيها وصولات)
            completed_payouts = InstructorPayout.objects.filter(
                instructor=user,
                status='completed',
                receipt__isnull=False  # فقط التحويلات اللي فيها وصولات
            ).select_related('processed_by').prefetch_related('orders__course').order_by('-created_at')

            payouts_data = []
            for payout in completed_payouts:
                # تجميع الكورسات والطلبات
                courses_info = {}
                for order in payout.orders.all():
                    course_id = str(order.course.id)
                    if course_id not in courses_info:
                        courses_info[course_id] = {
                            'course_title': order.course.title,
                            'orders_count': 0,
                            'total_amount': 0
                        }
                    courses_info[course_id]['orders_count'] += 1
                    courses_info[course_id]['total_amount'] += float(order.base_price) if order.base_price else float(order.amount)

                payouts_data.append({
                    'id': payout.id,
                    'amount_paid': float(payout.amount_paid),
                    'platform_fee': float(payout.platform_fee),
                    'total_amount': float(payout.total_amount),
                    'orders_count': payout.orders.count(),
                    'courses': list(courses_info.values()),
                    'receipt_url': request.build_absolute_uri(payout.receipt.url) if payout.receipt else None,
                    'notes': payout.notes,
                    'created_at': payout.created_at,
                    'completed_at': payout.completed_at,
                    'processed_by': payout.processed_by.username if payout.processed_by else None,
                })

            # إحصائيات عامة
            total_completed = completed_payouts.count()
            total_amount_received = sum(float(p.amount_paid) for p in completed_payouts)

            # التحويلات المعلقة (بدون وصولات)
            pending_payouts_count = InstructorPayout.objects.filter(
                instructor=user,
                status='completed',
                receipt__isnull=True  # التحويلات المكتملة لكن بدون وصولات
            ).count()

            return Response({
                'completed_payouts': payouts_data,
                'stats': {
                    'total_completed_transfers': total_completed,
                    'pending_receipts': pending_payouts_count,
                    'total_amount_received': round(total_amount_received, 2),
                    'transfers_with_receipts': total_completed,
                    'completion_rate': round((total_completed / (total_completed + pending_payouts_count) * 100) if (total_completed + pending_payouts_count) > 0 else 0, 1)
                }
            })

        except Exception as e:
            return Response(
                {"error": f"خطأ في جلب بيانات التحويلات: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InstructorCoursesAPIView(APIView):
    """API لجلب كورسات المعلم فقط - zaki alkholy"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user

            # التأكد من أن المستخدم معلم
            if not user.is_instructor:
                return Response(
                    {"error": "غير مصرح لك بالوصول لهذه البيانات"},
                    status=status.HTTP_403_FORBIDDEN
                )

            # جلب كورسات المعلم فقط
            courses = Course.objects.filter(instructor=user).order_by('-created_at')

            # استخدام CourseReadSerializer لإرجاع كل البيانات بما فيها الـ slug
            from ..serializers_file.course_serializer import CourseReadSerializer
            serializer = CourseReadSerializer(courses, many=True, context={'request': request})

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(
                {"error": f"خطأ في جلب الكورسات: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
