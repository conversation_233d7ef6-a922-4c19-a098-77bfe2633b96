from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from ..models import (
    ReviewSchedule,
    Lesson,
    Quiz,
    Course,
    Question,
    Answer,
)

from .user_serializer import UserSerializer

User = get_user_model()


class ReviewScheduleSerializer(serializers.ModelSerializer):
    """سيريالايزر لجدولة المراجعة المتباعدة"""
    lesson_title = serializers.CharField(source="lesson.title", read_only=True)
    course_title = serializers.CharField(source="lesson.course.title", read_only=True)
    quiz_title = serializers.CharField(source="quiz.title", read_only=True)
    days_until_review = serializers.SerializerMethodField()
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ReviewSchedule
        fields = [
            "id",
            "student",
            "lesson",
            "quiz",
            "lesson_title",
            "course_title",
            "quiz_title",
            "content_type",
            "content_title",
            "content_summary",
            "initial_learned_date",
            "next_review_date",
            "review_interval_days",
            "review_count",
            "success_count",
            "difficulty_level",
            "is_active",
            "is_mastered",
            "days_until_review",
            "success_rate",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "lesson_title",
            "course_title",
            "quiz_title",
            "days_until_review",
            "success_rate",
            "created_at",
            "updated_at",
        ]
    
    def get_days_until_review(self, obj):
        """عدد الأيام المتبقية للمراجعة"""
        if obj.next_review_date:
            time_diff = obj.next_review_date - timezone.now()
            return time_diff.days
        return 0
    
    def get_success_rate(self, obj):
        """معدل النجاح في المراجعة"""
        if obj.review_count > 0:
            return round((obj.success_count / obj.review_count) * 100, 2)
        return 0


class ReviewQuestionSerializer(serializers.ModelSerializer):
    """سيريالايزر لأسئلة المراجعة"""
    answers = serializers.SerializerMethodField()
    
    class Meta:
        model = Question
        fields = [
            "id",
            "text",
            "question_type",
            "image",
            "answers",
        ]
    
    def get_answers(self, obj):
        """الحصول على الإجابات (بدون إظهار الإجابة الصحيحة)"""
        answers = obj.answers.all()
        return [
            {
                "id": answer.id,
                "text": answer.text,
            }
            for answer in answers
        ]


class ReviewSessionSerializer(serializers.Serializer):
    """سيريالايزر لجلسة المراجعة"""
    review_schedule_id = serializers.UUIDField()
    questions = ReviewQuestionSerializer(many=True, read_only=True)
    content_summary = serializers.CharField(read_only=True)
    difficulty_level = serializers.IntegerField(read_only=True)
    review_count = serializers.IntegerField(read_only=True)
    
    def to_representation(self, instance):
        """تحضير بيانات جلسة المراجعة"""
        data = super().to_representation(instance)
        
        # إضافة أسئلة المراجعة إذا كان المحتوى اختبار
        if instance.quiz:
            questions = instance.quiz.questions.all()[:5]  # أول 5 أسئلة
            data['questions'] = ReviewQuestionSerializer(questions, many=True).data
        
        return data


class ReviewAnswerSerializer(serializers.Serializer):
    """سيريالايزر لإجابات المراجعة"""
    review_schedule_id = serializers.UUIDField()
    answers = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField()
        ),
        required=False
    )
    self_assessment = serializers.ChoiceField(
        choices=[
            ("easy", "سهل"),
            ("medium", "متوسط"),
            ("hard", "صعب"),
            ("forgot", "نسيت"),
        ]
    )
    
    def validate_review_schedule_id(self, value):
        """التحقق من وجود جدولة المراجعة"""
        try:
            review_schedule = ReviewSchedule.objects.get(id=value)
            return value
        except ReviewSchedule.DoesNotExist:
            raise serializers.ValidationError("جدولة المراجعة غير موجودة")
    
    def save(self):
        """حفظ نتائج المراجعة وتحديث الجدولة"""
        review_schedule_id = self.validated_data['review_schedule_id']
        answers = self.validated_data.get('answers', [])
        self_assessment = self.validated_data['self_assessment']
        
        review_schedule = ReviewSchedule.objects.get(id=review_schedule_id)
        
        # تقييم الأداء
        success = False
        
        if review_schedule.quiz and answers:
            # تقييم الإجابات للاختبار
            correct_answers = 0
            total_questions = len(answers)
            
            for answer_data in answers:
                question_id = answer_data.get('question_id')
                answer_id = answer_data.get('answer_id')
                
                try:
                    question = Question.objects.get(id=question_id)
                    correct_answer = question.answers.filter(is_correct=True).first()
                    if correct_answer and str(correct_answer.id) == answer_id:
                        correct_answers += 1
                except Question.DoesNotExist:
                    continue
            
            # النجاح إذا كانت الإجابات الصحيحة أكثر من 70%
            success = (correct_answers / total_questions) >= 0.7 if total_questions > 0 else False
        else:
            # تقييم بناءً على التقييم الذاتي
            success = self_assessment in ["easy", "medium"]
        
        # تحديث جدولة المراجعة
        review_schedule.update_schedule(success=success)
        
        # إضافة نقاط للطالب
        if success:
            from ..models import StudentPoints
            points_profile, _ = StudentPoints.objects.get_or_create(student=review_schedule.student)
            points_profile.add_points(5, f"مراجعة ناجحة: {review_schedule.content_title}")
        
        return {
            "success": success,
            "next_review_date": review_schedule.next_review_date,
            "difficulty_level": review_schedule.difficulty_level,
            "is_mastered": review_schedule.is_mastered,
        }


class DailyReviewSerializer(serializers.Serializer):
    """سيريالايزر للمراجعة اليومية"""
    date = serializers.DateField(read_only=True)
    total_reviews = serializers.IntegerField(read_only=True)
    completed_reviews = serializers.IntegerField(read_only=True)
    pending_reviews = serializers.ListField(
        child=ReviewScheduleSerializer(),
        read_only=True
    )
    overdue_reviews = serializers.ListField(
        child=ReviewScheduleSerializer(),
        read_only=True
    )
    
    def to_representation(self, instance):
        """تحضير بيانات المراجعة اليومية"""
        student = instance
        today = timezone.now().date()
        
        # المراجعات المطلوبة اليوم
        today_reviews = ReviewSchedule.objects.filter(
            student=student,
            is_active=True,
            next_review_date__date=today
        )
        
        # المراجعات المتأخرة
        overdue_reviews = ReviewSchedule.objects.filter(
            student=student,
            is_active=True,
            next_review_date__date__lt=today
        )
        
        # المراجعات المكتملة اليوم (نحتاج لتتبع هذا في نموذج منفصل)
        completed_today = 0  # يمكن تطويره لاحقاً
        
        return {
            "date": today,
            "total_reviews": today_reviews.count() + overdue_reviews.count(),
            "completed_reviews": completed_today,
            "pending_reviews": ReviewScheduleSerializer(today_reviews, many=True).data,
            "overdue_reviews": ReviewScheduleSerializer(overdue_reviews, many=True).data,
        }


class CreateReviewScheduleSerializer(serializers.ModelSerializer):
    """سيريالايزر لإنشاء جدولة مراجعة جديدة"""
    
    class Meta:
        model = ReviewSchedule
        fields = [
            "lesson",
            "quiz",
            "content_type",
            "content_title",
            "content_summary",
        ]
    
    def create(self, validated_data):
        """إنشاء جدولة مراجعة جديدة"""
        student = self.context['request'].user
        lesson = validated_data['lesson']
        
        # تحديد موعد المراجعة الأولى (بعد يوم واحد)
        initial_date = timezone.now()
        next_review = initial_date + timedelta(days=1)
        
        # إنشاء الجدولة
        review_schedule = ReviewSchedule.objects.create(
            student=student,
            lesson=lesson,
            quiz=validated_data.get('quiz'),
            content_type=validated_data['content_type'],
            content_title=validated_data['content_title'],
            content_summary=validated_data.get('content_summary', ''),
            initial_learned_date=initial_date,
            next_review_date=next_review,
            review_interval_days=1,
        )
        
        return review_schedule


class ReviewStatsSerializer(serializers.Serializer):
    """سيريالايزر لإحصائيات المراجعة"""
    total_items = serializers.IntegerField()
    mastered_items = serializers.IntegerField()
    active_items = serializers.IntegerField()
    overdue_items = serializers.IntegerField()
    today_reviews = serializers.IntegerField()
    success_rate = serializers.FloatField()
    average_difficulty = serializers.FloatField()
    longest_streak = serializers.IntegerField()
    
    def to_representation(self, instance):
        """حساب إحصائيات المراجعة للطالب"""
        student = instance
        today = timezone.now().date()
        
        # الإحصائيات الأساسية
        all_reviews = ReviewSchedule.objects.filter(student=student)
        
        total_items = all_reviews.count()
        mastered_items = all_reviews.filter(is_mastered=True).count()
        active_items = all_reviews.filter(is_active=True).count()
        overdue_items = all_reviews.filter(
            is_active=True,
            next_review_date__date__lt=today
        ).count()
        today_reviews = all_reviews.filter(
            is_active=True,
            next_review_date__date=today
        ).count()
        
        # معدل النجاح العام
        total_reviews = sum(r.review_count for r in all_reviews)
        total_successes = sum(r.success_count for r in all_reviews)
        success_rate = (total_successes / total_reviews * 100) if total_reviews > 0 else 0
        
        # متوسط الصعوبة
        active_reviews = all_reviews.filter(is_active=True)
        avg_difficulty = (
            sum(r.difficulty_level for r in active_reviews) / active_reviews.count()
            if active_reviews.count() > 0 else 0
        )
        
        return {
            "total_items": total_items,
            "mastered_items": mastered_items,
            "active_items": active_items,
            "overdue_items": overdue_items,
            "today_reviews": today_reviews,
            "success_rate": round(success_rate, 2),
            "average_difficulty": round(avg_difficulty, 2),
            "longest_streak": 0,  # يمكن تطويره لاحقاً
        }
