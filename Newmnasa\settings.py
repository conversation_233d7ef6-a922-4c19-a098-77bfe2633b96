import os
from pathlib import Path
from datetime import timedelta
from decouple import config
import cloudinary

BASE_DIR = Path(__file__).resolve().parent.parent

# أمان
SECRET_KEY = config("SECRET_KEY")
DEBUG = config("DEBUG", default=False, cast=bool)
# ALLOWED_HOSTS = config("ALLOWED_HOSTS", default="127.0.0.1").split(",")
ALLOWED_HOSTS = [
    # ... أي دومينات أخرى ...
    "4467fe94c325.ngrok-free.app",
    "localhost",
    "127.0.0.1",
]

# التطبيقات
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "rest_framework_simplejwt",
    "corsheaders",
    "cloudinary",
    "cloudinary_storage",
    "main",
    # Hossam Start Google Authentication
    # تطبيقات Allauth المطلوبة
    "django.contrib.sites",  # Allauth يتطلب هذا
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "allauth.socialaccount.providers.google",  # هذا خاص بـ Google
    # Security
    "axes",
]
SITE_ID = 1  # مهم لـ Allauth

# إعدادات Allauth إضافية (يمكنك تخصيصها حسب الحاجة)
AUTHENTICATION_BACKENDS = (
    # المطلوب لـ allauth لإدارة تسجيل الدخول
    "allauth.account.auth_backends.AuthenticationBackend",
    # هذا يسمح لك بتسجيل الدخول باستخدام اسم المستخدم الافتراضي/البريد الإلكتروني
    "django.contrib.auth.backends.ModelBackend",
)

LOGIN_REDIRECT_URL = (
    "http://localhost:3000/"  # الصفحة التي يتم التوجيه إليها بعد تسجيل الدخول بنجاح
)
ACCOUNT_LOGOUT_REDIRECT_URL = (
    "http://localhost:3000/login"  # الصفحة التي يتم التوجيه إليها بعد تسجيل الخروج
)
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_USERNAME_REQUIRED = False  # يمكن أن يكون البريد الإلكتروني هو المعرف الأساسي
ACCOUNT_AUTHENTICATION_METHOD = "email"
ACCOUNT_LOGIN_METHODS = {"email"}
ACCOUNT_SIGNUP_FIELDS = ["email*", "password1*", "password2*"]
ACCOUNT_EMAIL_VERIFICATION = "none"  # أو 'optional' أو 'mandatory'
SOCIALACCOUNT_QUERY_EMAIL = True

# Verfication Email

# تعطيل إرسال الإيميل مؤقتاً للاختبار - zaki alkholy
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
# EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.gmail.com"
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = "<EMAIL>"
EMAIL_HOST_PASSWORD = "rsae ctzv vbmm dtuw"
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER
# رابط الواجهة الأمامية
FRONTEND_URL = "http://localhost:3000"


# ميدلوير
MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
    # Security
    "axes.middleware.AxesMiddleware",
]
# إعدادات الحماية من هجمات Brute Force - zaki alkholy
AXES_FAILURE_LIMIT = 3  # عدد المحاولات المسموحة (قللناها من 5 إلى 3)
AXES_COOLOFF_TIME = 1  # وقت الحظر بالساعات (قللناها من 3 إلى 1)
AXES_LOCKOUT_CALLABLE = None
AXES_USE_USER_AGENT = True
AXES_LOCK_OUT_BY_COMBINATION_USER_AND_IP = True
AXES_ENABLE_ADMIN = True  # تفعيل واجهة الإدارة لمراقبة المحاولات
AXES_VERBOSE = True  # تسجيل تفصيلي للمحاولات المشبوهة
ROOT_URLCONF = "Newmnasa.urls"

# القوالب
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    }
]

WSGI_APPLICATION = "Newmnasa.wsgi.application"

# قاعدة البيانات: SQLite أو PostgreSQL
DATABASE_URL = config("DATABASE_URL", default="sqlite:///db.sqlite3")

if DATABASE_URL.startswith("sqlite"):
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",
        }
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": config("DB_NAME"),
            "USER": config("DB_USER"),
            "PASSWORD": config("DB_PASSWORD"),
            "HOST": config("DB_HOST"),
            "PORT": config("DB_PORT"),
        }
    }

# التحقق من كلمات المرور
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# اللغة والتوقيت
LANGUAGE_CODE = "ar"
TIME_ZONE = "Asia/Riyadh"
USE_I18N = True
USE_TZ = True

# ملفات static و media
STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "staticfiles"
STATICFILES_DIRS = [BASE_DIR / "static"]

MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "media"

# Cloudinary
CLOUDINARY_STORAGE = {
    "CLOUD_NAME": config("CLOUDINARY_CLOUD_NAME"),
    "API_KEY": config("CLOUDINARY_API_KEY"),
    "API_SECRET": config("CLOUDINARY_API_SECRET"),
    "STATICFILES_MANIFEST_ROOT": os.path.join(BASE_DIR, "manifest"),
    "VIDEO_OPTIONS": {
        "resource_type": "video",
        "quality": "auto",
    },
}

cloudinary.config(
    cloud_name=config("CLOUDINARY_CLOUD_NAME"),
    api_key=config("CLOUDINARY_API_KEY"),
    api_secret=config("CLOUDINARY_API_SECRET"),
)

DEFAULT_FILE_STORAGE = "cloudinary_storage.storage.MediaCloudinaryStorage"

# المستخدم المخصص
AUTH_USER_MODEL = "main.User"

# CORS

CORS_ALLOWED_ORIGINS = config("CORS_ALLOWED_ORIGINS").split(",")
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]
CORS_ALLOW_METHODS = ["DELETE", "GET", "OPTIONS", "PATCH", "POST", "PUT"]
CORS_ALLOW_ALL_ORIGINS = True

# REST framework
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": ["rest_framework.permissions.IsAuthenticated"],
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
        "rest_framework.renderers.BrowsableAPIRenderer",
    ],
    # تعطيل الـ throttling مؤقتاً للاختبار - zaki alkholy
    # "DEFAULT_THROTTLE_CLASSES": [
    #     "rest_framework.throttling.UserRateThrottle",
    #     "rest_framework.throttling.AnonRateThrottle",
    # ],
    # "DEFAULT_THROTTLE_RATES": {
    #     "user": "100/hour",
    #     "anon": "20/hour",
    #     "search_users": "20/min",
    #     # حماية إضافية لتسجيل الدخول - zaki alkholy
    #     "login": "5/min",  # 5 محاولات تسجيل دخول كل دقيقة
    #     "register": "3/min",  # 3 محاولات تسجيل كل دقيقة
    # },
}

# JWT إعدادات
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(
        hours=config("JWT_ACCESS_TOKEN_LIFETIME", cast=int, default=24)
    ),
    "REFRESH_TOKEN_LIFETIME": timedelta(
        days=config("JWT_REFRESH_TOKEN_LIFETIME", cast=int, default=7)
    ),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
}

# Paymob
PAYMOB_API_KEY = config("PAYMOB_API_KEY")
PAYMOB_PUBLIC_KEY = config("PAYMOB_PUBLIC_KEY")
PAYMOB_INTEGRATION_ID = config("PAYMOB_INTEGRATION_ID")
PAYMOB_IFRAME_ID = config("PAYMOB_IFRAME_ID")
PAYMOB_HMAC_SECRET = config("PAYMOB_HMAC_SECRET")
PAYMOB_BASE_URL = "https://accept.paymob.com/api/"
PLATFORM_FEE_PERCENTAGE = 1

# =============================
# Paymob Payout (Disbursement) - تم تعطيله مؤقتاً - zaki alkholy
# =============================
# PAYMOB_PAYOUT_BASE_URL = config("PAYMOB_PAYOUT_BASE_URL", default=None)
# PAYMOB_PAYOUT_CLIENT_ID = config("PAYMOB_PAYOUT_CLIENT_ID", default=None)
# PAYMOB_PAYOUT_CLIENT_SECRET = config("PAYMOB_PAYOUT_CLIENT_SECRET", default=None)
# PAYMOB_PAYOUT_USERNAME = config("PAYMOB_PAYOUT_USERNAME", default=None)
# PAYMOB_PAYOUT_PASSWORD = config("PAYMOB_PAYOUT_PASSWORD", default=None)

# Frontend
# FRONTEND_URL = config("FRONTEND_URL", default="http://localhost:3000")

# خط الشهادات
FONT_PATH = os.path.join(BASE_DIR, "static/fonts/arial.ttf")

# تسجيل الأخطاء
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": BASE_DIR / "logs/error.log",
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "main": {
            "handlers": ["file", "console"],
            "level": "DEBUG",
            "propagate": True,
        },
    },
}

# Celery
CELERY_BROKER_URL = "redis://localhost:6379/0"
CELERY_RESULT_BACKEND = "redis://localhost:6379/0"

# Celery Beat Schedule - zaki alkholy
from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    "auto-submit-expired-exams": {
        "task": "main.tasks.auto_submit_expired_exams",
        "schedule": crontab(minute="*/5"),  # كل 5 دقائق
    },
}

CELERY_TIMEZONE = "UTC"

# =============================
# إعدادات Bunny Stream للفيديوهات المدفوعة - zaki alkholy
# =============================
BUNNY_STREAM = {
    "API_KEY": config(
        "BUNNY_STREAM_API_KEY", default="a8488f72-1f81-44bf-b2f516a63934-1ecd-4c9c"
    ),
    "LIBRARY_ID": config("BUNNY_STREAM_LIBRARY_ID", default="466765"),
    "CDN_HOSTNAME": config(
        "BUNNY_STREAM_CDN_HOSTNAME", default="vz-bbc4351e-8f1.b-cdn.net"
    ),
    "BASE_URL": "https://video.bunnycdn.com",
    "STREAM_URL": "https://iframe.mediadelivery.net",
    # إعدادات الأمان والتشفير - zaki alkholy
    "ENABLE_DRM": True,
    "ENABLE_TOKEN_AUTHENTICATION": True,
    "TOKEN_EXPIRY_HOURS": 24,
    "ENABLE_WATERMARK": True,
    "WATERMARK_TEXT": "{student_name}",  # سيتم استبداله باسم الطالب
    "WATERMARK_OPACITY": 0.7,
    "WATERMARK_POSITION": "top-right",
    # إعدادات الجودة والتشفير - zaki alkholy
    "ENABLE_MP4_FALLBACK": True,
    "ENABLE_ADAPTIVE_STREAMING": True,
    "RESOLUTIONS": ["240p", "360p", "480p", "720p", "1080p"],
    "DEFAULT_RESOLUTION": "720p",
}
