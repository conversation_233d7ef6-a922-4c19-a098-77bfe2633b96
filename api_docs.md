وثائق API لمنصة التعلم الإلكتروني
المصادقة والتسجيل
تسجيل مستخدم جديد

URL: /api/auth/register/
Method: POST
Content-Type: multipart/form-data
Body:

username: string
email: string
password: string
phone_number: string (optional)
profile_image: file (optional)
is_instructor: boolean (optional, default: false)


Response (201 Created):

{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "phone_number": "string",
    "profile_image": "url",
    "is_instructor": boolean,
    "token": {
        "access": "string",
        "refresh": "string"
    }
}

تسجيل الدخول

URL: /api/auth/login/
Method: POST
Content-Type: application/json
Body:

{
  "username": "string",
  "password": "string"
}


Response (200 OK):

{
    "token": {
        "access": "string",
        "refresh": "string"
    },
    "user": {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "is_instructor": boolean,
        "profile_image": "url"
    }
}

المستخدمين
الحصول على معلومات المستخدم

URL: /api/users/{user_id}/

Method: GET

Headers: Authorization: Bearer {token}

Response (200 OK):


{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "phone_number": "string",
    "profile_image": "url",
    "bio": "string",
    "date_of_birth": "date",
    "language": "string",
    "is_instructor": boolean,
    "created_at": "datetime"
}

تحديث معلومات المستخدم

URL: /api/users/{user_id}/
Method: PUT
Headers: Authorization: Bearer {token}
Body:

{
  "phone_number": "string",
  "profile_image": "file",
  "bio": "string",
  "date_of_birth": "date",
  "language": "string"
}


Response (200 OK):

{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "phone_number": "string",
    "profile_image": "url",
    "bio": "string",
    "date_of_birth": "date",
    "language": "string",
    "is_instructor": boolean,
    "updated_at": "datetime"
}

تحديث ملف المدرس

URL: /api/instructors/{user_id}/
Method: PUT
Headers: Authorization: Bearer {token}
Body:

{
  "specialization": "string",
  "qualifications": "string",
  "website": "string",
  "linkedin": "string",
  "payment_method": "string (BANK_ACCOUNT, VODAFONE_CASH, ETISALAT_CASH)",
  "payment_details": {
    "phone_number": "string",
    "account_number": "string",
    "bank_name": "string"
  }
}


Response (200 OK):

{
    "id": "uuid",
    "user": "uuid",
    "specialization": "string",
    "qualifications": "string",
    "website": "string",
    "linkedin": "string",
    "payment_method": "string",
    "payment_details": {
      "phone_number": "string",
      "account_number": "string",
      "bank_name": "string"
    },
    "is_approved": boolean
}

الدورات
الحصول على قائمة الدورات

URL: /api/courses/

Method: GET

Query Parameters:

category: string (optional)
level: string (optional)
language: string (optional)
instructor: uuid (optional)


Response (200 OK):


{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "title": "string",
            "description": "string",
            "short_description": "string",
            "category": {
                "id": "uuid",
                "name": "string"
            },
            "price": number,
            "discount_price": number,
            "currency": "string",
            "level": "string",
            "language": "string",
            "thumbnail": "url",
            "promo_video": "url",
            "instructor": {
                "id": "uuid",
                "username": "string"
            },
            "rating": number,
            "students_count": number,
            "created_at": "datetime"
        }
    ]
}

إنشاء دورة جديدة

URL: /api/courses/
Method: POST
Headers: 
Authorization: Bearer {token}


Content-Type: multipart/form-data
Body:

title: string (required)
description: string (required)
short_description: string (optional)
category: uuid (required)
price: number (required)
discount_price: number (optional)
currency: string (default: "USD")
level: string (required, choices: ["beginner", "intermediate", "advanced"])
language: string (default: "Arabic")
prerequisites: string (optional)
learning_outcomes: string (optional)
max_students: number (optional)
thumbnail: file (required, formats: jpg, jpeg, png)
promo_video: file (optional, formats: mp4, mov, avi)
is_published: boolean (optional, default: false)


Response (201 Created):

{
    "id": "uuid",
    "title": "string",
    "description": "string",
    "short_description": "string",
    "category": {
        "id": "uuid",
        "name": "string"
    },
    "price": number,
    "discount_price": number,
    "currency": "string",
    "level": "string",
    "language": "string",
    "prerequisites": "string",
    "learning_outcomes": "string",
    "max_students": number,
    "thumbnail": "cloudinary_url",
    "promo_video": "cloudinary_url",
    "instructor": {
        "id": "uuid",
        "username": "string"
    },
    "created_at": "datetime"
}

تحديث دورة

URL: /api/courses/{course_id}/
Method: PUT
Headers: Authorization: Bearer {token}
Body: نفس هيكل إنشاء دورة جديدة

حذف دورة

URL: /api/courses/{course_id}/
Method: DELETE
Headers: Authorization: Bearer {token}

الدروس
الحصول على دروس الدورة

URL: /api/courses/{course_id}/lessons/

Method: GET

Headers: Authorization: Bearer {token}

Response (200 OK):


{
    "count": number,
    "next": "url",
    "previous": "url",
    "results": [
        {
            "id": "uuid",
            "title": "string",
            "order": number,
            "lesson_type": "string",
            "content": "string",
            "is_preview": boolean,
            "duration": number,
            "resources": ["url"],
            "created_at": "datetime"
        }
    ]
}

إنشاء درس جديد

URL: /api/courses/{course_id}/lessons/
Method: POST

