# Django REST framework
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions, status
from rest_framework.permissions import AllowAny
from django.utils import timezone
from datetime import timedelta

from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
import logging


# JWT
from rest_framework_simplejwt.tokens import RefreshToken

# Django core
from django.conf import settings
from django.contrib.auth import authenticate, get_user_model
from django.core.mail import send_mail

# Google Social Login
from google.oauth2 import id_token
from google.auth.transport import requests

# Serializers
from ..serializers import UserSerializer

User = get_user_model()
# Utils
import secrets
import time
import os

GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_CLIENT_ID")


# إعداد logger للأمان - zaki alkholy
security_logger = logging.getLogger('main')

# Throttle class مخصص لتسجيل الدخول - zaki alkholy
class LoginRateThrottle(AnonRateThrottle):
    scope = 'login'

# Throttle class مخصص للتسجيل - zaki alkholy
class RegisterRateThrottle(AnonRateThrottle):
    scope = 'register'


class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]
    # تعطيل throttle مؤقتاً للاختبار - zaki alkholy
    # throttle_classes = [RegisterRateThrottle]

    def post(self, request):
        serializer = UserSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()

            # Generate verification token with expiry (24 hours)
            token = secrets.token_urlsafe(32)
            user.verification_token = token
            user.email_verified = False
            # Set token expiry to 24 hours from now
            user.verification_token_expiry = timezone.now() + timedelta(hours=24)
            user.save()

            # Send verification email
            verification_url = f"{settings.FRONTEND_URL}/verify-email?token={token}"
            send_mail(
                subject="تفعيل حسابك في منصة مُعَلِّمِيّ",
                message=f"""
مرحباً {user.first_name or user.username}،

شكراً لك على التسجيل في منصة مُعَلِّمِيّ!

لإكمال عملية التسجيل، يرجى النقر على الرابط التالي لتفعيل حسابك:
{verification_url}

هذا الرابط صالح لمدة 24 ساعة فقط.

إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.

مع تحيات فريق منصة مُعَلِّمِيّ
                """,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )

            # لا نرجع JWT tokens - المستخدم يحتاج لتفعيل البريد أولاً
            return Response(
                {
                    "message": "تم إنشاء الحساب بنجاح! تم إرسال رابط التفعيل إلى بريدك الإلكتروني. يرجى التحقق من بريدك الإلكتروني وتفعيل حسابك قبل تسجيل الدخول.",
                    "email": user.email,
                    "verification_required": True
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(ratelimit(key="ip", rate="10/m", block=True), name='get')
class VerifyEmailView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        token = request.query_params.get("token")

        if not token:
            return Response({"error": "التوكن مفقود"}, status=400)

        try:
            user = User.objects.get(verification_token=token)

            if (
                user.verification_token_expiry
                and user.verification_token_expiry < timezone.now()
            ):
                return Response({
                    "error": "انتهت صلاحية رابط التفعيل",
                    "expired": True,
                    "email": user.email
                }, status=400)

            user.email_verified = True
            user.verification_token = None
            user.verification_token_expiry = None
            user.save()

            security_logger.info(f"Email verification successful for user: {user.email}")
            return Response({"message": "تم تأكيد البريد الإلكتروني بنجاح"})
        except User.DoesNotExist:
            return Response({"error": "توكين غير صالح"}, status=400)


@method_decorator(ratelimit(key="ip", rate="3/h", block=True), name='post')
class ResendVerificationEmailView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        email = request.data.get("email", "").strip().lower()

        if not email:
            return Response({"error": "البريد الإلكتروني مطلوب"}, status=400)

        try:
            user = User.objects.get(email__iexact=email)

            if user.email_verified:
                return Response({"error": "البريد الإلكتروني مفعل بالفعل"}, status=400)

            # Generate new verification token
            token = secrets.token_urlsafe(32)
            user.verification_token = token
            user.verification_token_expiry = timezone.now() + timedelta(hours=24)
            user.save()

            # Send verification email
            verification_url = f"{settings.FRONTEND_URL}/verify-email?token={token}"
            send_mail(
                subject="إعادة إرسال رابط تفعيل حسابك في منصة مُعَلِّمِيّ",
                message=f"""
مرحباً {user.first_name or user.username}،

تم طلب إعادة إرسال رابط تفعيل حسابك في منصة مُعَلِّمِيّ.

يرجى النقر على الرابط التالي لتفعيل حسابك:
{verification_url}

هذا الرابط صالح لمدة 24 ساعة فقط.

إذا لم تطلب هذا، يرجى تجاهل هذا البريد الإلكتروني.

مع تحيات فريق منصة مُعَلِّمِيّ
                """,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )

            return Response({
                "message": "تم إعادة إرسال رابط التفعيل إلى بريدك الإلكتروني"
            })

        except User.DoesNotExist:
            return Response({"error": "البريد الإلكتروني غير مسجل"}, status=404)


class RequestPasswordResetView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        email = request.data.get("email")
        try:
            email = request.data.get("email", "").strip().lower()
            user = User.objects.filter(email__iexact=email).first()
            if not user:
                return Response({"error": "هذا البريد غير مسجل لدينا."}, status=404)

            token = secrets.token_urlsafe(32)
            user.reset_token = token
            user.save()

            reset_url = f"{settings.FRONTEND_URL}/reset-password?token={token}"

            send_mail(
                "إعادة تعيين كلمة المرور",
                f"اضغط على الرابط التالي لإعادة تعيين كلمة المرور: {reset_url}",
                settings.DEFAULT_FROM_EMAIL,
                [email],
                fail_silently=False,
            )
            return Response(
                {
                    "message": "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني."
                }
            )
        except User.DoesNotExist:
            return Response({"error": "هذا البريد غير مسجل لدينا."}, status=404)


class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        token = request.data.get("token")
        new_password = request.data.get("new_password")

        try:
            user = User.objects.get(reset_token=token)
            user.set_password(new_password)
            user.reset_token = None
            user.save()
            return Response({"message": "تم تغيير كلمة المرور بنجاح."})
        except User.DoesNotExist:
            return Response({"error": "رابط غير صالح أو منتهي."}, status=400)


class LoginView(APIView):
    permission_classes = [permissions.AllowAny]
    # تعطيل الـ throttle مؤقتاً للاختبار - zaki alkholy
    # throttle_classes = [LoginRateThrottle]  # حماية من Brute Force - zaki alkholy

    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")

        # الحصول على IP للتسجيل - zaki alkholy
        client_ip = request.META.get('HTTP_X_FORWARDED_FOR',
                                   request.META.get('REMOTE_ADDR', 'Unknown'))

        user = authenticate(username=username, password=password)

        if user:
            # التحقق من تفعيل البريد الإلكتروني
            if not user.email_verified:
                security_logger.warning(f"🚨 Login attempt with unverified email: {username} from IP: {client_ip}")
                return Response(
                    {
                        "error": "يجب تفعيل البريد الإلكتروني أولاً. يرجى التحقق من بريدك الإلكتروني والنقر على رابط التفعيل.",
                        "email_not_verified": True
                    },
                    status=status.HTTP_403_FORBIDDEN
                )

            # تسجيل نجاح تسجيل الدخول - zaki alkholy
            security_logger.info(f"✅ Successful login: {username} from IP: {client_ip}")

            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "user": UserSerializer(user, context={"request": request}).data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                }
            )

        # تسجيل محاولة تسجيل دخول فاشلة - zaki alkholy
        security_logger.warning(f"🚨 Failed login attempt: {username} from IP: {client_ip}")

        return Response(
            {"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED
        )


class GoogleSocialLoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def verify_token_with_retry(self, token, max_retries=7, delay=1):
        for attempt in range(max_retries):
            try:
                idinfo = id_token.verify_oauth2_token(
                    token,
                    requests.Request(),
                    GOOGLE_CLIENT_ID,
                )
                return idinfo
            except ValueError as e:
                if "used too early" in str(e) and attempt < max_retries - 1:
                    time.sleep(delay)
                else:
                    raise e

    def post(self, request):
        token = request.data.get("id_token")

        if not token:
            return Response({"error": "Token missing"}, status=400)

        try:
            idinfo = self.verify_token_with_retry(token)

            email = idinfo["email"]
            name = idinfo.get("name", "")
            picture = idinfo.get("picture", "")

            User = get_user_model()

            user, created = User.objects.get_or_create(
                email=email,
                defaults={
                    "username": email.split("@")[0],
                    "first_name": name,
                    "last_name": idinfo.get("family_name", ""),
                    "is_student": True,
                    "is_instructor": False,
                    "language": "ar",
                    "bio": "",
                    "phone_number": None,
                    "date_of_birth": None,
                    "email_verified": True,  # Google accounts are pre-verified
                },
            )

            refresh = RefreshToken.for_user(user)

            return Response(
                {
                    "user": UserSerializer(user, context={"request": request}).data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                }
            )

        except ValueError as e:
            return Response({"error": "Invalid token"}, status=401)
