import cloudinary
import cloudinary.uploader
import os
import glob
from django.conf import settings
from .video_converter import VideoConverter
import tempfile


class CloudinaryVideoService:
    """
    خدمة Cloudinary للصور والفيديوهات الترويجية فقط - زكي الخولي
    ملاحظة: الفيديوهات المدفوعة تستخدم Bunny Stream للحماية القصوى
    """

    @staticmethod
    def get_hls_url(public_id):
        try:
            cloud_name = settings.CLOUDINARY_STORAGE["CLOUD_NAME"]
            return f"https://res.cloudinary.com/{cloud_name}/video/upload/v1750794040/{public_id}.m3u8"
        # "https://res.cloudinary.com/di5y7hnub/video/upload/v1750794040/course_123/lessons/tmps_mf0inl.m3u8
        except Exception as e:
            raise Exception(f"Failed to generate HLS URL: {str(e)}")

    # @staticmethod
    # def get_hls_url(public_id, version):
    #     """
    #     توليد رابط HLS باستخدام version (v...) لزيادة الأمان وتقليل التخزين المؤقت
    #     """
    #     try:
    #         cloud_name = settings.CLOUDINARY_STORAGE["CLOUD_NAME"]
    #         return f"https://res.cloudinary.com/{cloud_name}/video/upload/v{version}/{public_id}.m3u8"
    #     except Exception as e:
    #         raise Exception(f"Failed to generate HLS URL with version: {str(e)}")

    @staticmethod
    def upload_promo_video(video_file, folder="promo_videos/"):
        """
        رفع فيديو ترويجي إلى Cloudinary (للفيديوهات المجانية فقط) - زكي الخولي
        ملاحظة: هذه الدالة للفيديوهات الترويجية المجانية فقط
        الفيديوهات المدفوعة تستخدم Bunny Stream
        """
        try:
            # 1. حفظ الفيديو مؤقتًا
            temp_fd, temp_path = tempfile.mkstemp(suffix=".mp4")
            with os.fdopen(temp_fd, "wb") as f:
                f.write(video_file.read())

            # 2. رفع الفيديو الترويجي إلى Cloudinary - زكي الخولي
            file_name = os.path.splitext(os.path.basename(temp_path))[0]
            public_id = os.path.join(folder, file_name).replace("\\", "/")

            result = cloudinary.uploader.upload(
                temp_path,
                resource_type="video",
                public_id=public_id,
                overwrite=True,
                eager=[
                    {"format": "mp4", "quality": "auto"},
                    {"format": "m3u8", "streaming_profile": "auto"},
                ],
                eager_async=True,  # توليد الـ HLS بشكل غير متزامن - زكي الخولي
                # إضافة watermark للفيديوهات الترويجية - زكي الخولي
                transformation=[
                    {
                        "overlay": "text:Arial_20:منصة تعليمية",
                        "gravity": "south_east",
                        "opacity": 60,
                    }
                ],
            )

            # 3. حذف الملف المؤقت
            os.remove(temp_path)

            # 4. إنشاء رابط HLS للفيديو الترويجي - زكي الخولي
            cloud_name = settings.CLOUDINARY_STORAGE["CLOUD_NAME"]
            hls_url = f"https://res.cloudinary.com/{cloud_name}/video/upload/f_auto:video,q_auto/manifest/{public_id}.m3u8"

            return {
                "secure_url": result["secure_url"],
                "public_id": result["public_id"],
                "duration": result.get("duration", 0),
                "format": result.get("format", "mp4"),
                "hls_url": hls_url,
                "video_type": "promo",  # تحديد نوع الفيديو - زكي الخولي
            }

        except Exception as e:
            raise Exception(f"Failed to upload promo video: {str(e)}")

    # تعليق الدالة القديمة بدلاً من حذفها - زكي الخولي
    # @staticmethod
    # def upload_video_as_hls(video_file, folder="course_videos/"):
    #     """
    #     الدالة القديمة لرفع الفيديوهات - معلقة للرجوع إليها عند الحاجة - زكي الخولي
    #     الآن نستخدم upload_promo_video للفيديوهات الترويجية
    #     و BunnyStreamService للفيديوهات المدفوعة
    #     """
    #     pass
