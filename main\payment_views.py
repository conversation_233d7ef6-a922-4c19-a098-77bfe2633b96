import hmac
import hashlib
import logging
import requests
import json
import os
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from .models import Order, Course

from .serializers import OrderSerializer

# تعديل بواسطة Zaki Alkholy: إضافة Logging لكل Webhook يصلك في ملف webhook_logs.log
webhook_logger = logging.getLogger("webhook_logger")
webhook_handler = logging.FileHandler("webhook_logs.log")
webhook_handler.setFormatter(logging.Formatter("%(asctime)s %(levelname)s %(message)s"))
if not webhook_logger.hasHandlers():
    webhook_logger.addHandler(webhook_handler)
webhook_logger.setLevel(logging.INFO)

logger = logging.getLogger(__name__)


def verify_hmac(request):
    received_hmac = request.GET.get("hmac") or request.headers.get("HMAC")
    if not received_hmac:
        logger.error("No HMAC signature provided")
        return False
    data = request.GET.dict()
    concatenated = (
        f"{data.get('amount_cents','')}"
        f"{data.get('created_at','')}"
        f"{data.get('currency','')}"
        f"{data.get('error_occured','')}"
        f"{data.get('has_parent_transaction','')}"
        f"{data.get('id','')}"
        f"{data.get('integration_id','')}"
        f"{data.get('is_3d_secure','')}"
        f"{data.get('is_auth','')}"
        f"{data.get('is_capture','')}"
        f"{data.get('is_refunded','')}"
        f"{data.get('is_standalone_payment','')}"
        f"{data.get('is_voided','')}"
        f"{data.get('order','')}"
        f"{data.get('owner','')}"
        f"{data.get('pending','')}"
        f"{data.get('source_data.pan','')}"
        f"{data.get('source_data.sub_type','')}"
        f"{data.get('source_data.type','')}"
        f"{data.get('success','')}"
    )
    computed_hmac = hmac.new(
        settings.PAYMOB_HMAC_SECRET.encode(), concatenated.encode(), hashlib.sha512
    ).hexdigest()
    return hmac.compare_digest(computed_hmac, received_hmac)


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def create_payment_intent(request):
    try:
        course_id = request.data.get("course_id")
        payment_method = request.data.get("payment_method", "vodafone_cash")
        phone_number = request.data.get("phone_number")
        course = Course.objects.get(id=course_id)
        # السعر الأصلي
        # base_price = float(course.price)
        # # حساب الرسوم
        # paymob_fee = round(base_price * 0.03, 2)
        # platform_fee = round(base_price * 0.05, 2)
        # total_price = round(base_price + paymob_fee + platform_fee, 2)
        # amount_cents = int(total_price * 100)
        # currency = "EGP"

        # السعر بعد الخصم (إذا وجد)
        if course.discount_price:
            base_price = float(course.discount_price)
        else:
            base_price = float(course.price)

        # حساب المبالغ المطلوبة:
        # المنصة تكسب 5% من السعر الأصلي
        platform_fee = round(base_price * 0.05, 2)

        # المعلم يكسب الباقي (95%)
        instructor_earnings = round(base_price * 0.95, 2)

        # المجموع المطلوب وصوله = السعر الأصلي
        required_net_amount = base_price

        # حساب المبلغ الذي يجب على الطالب دفعه
        # المعادلة: المبلغ المدفوع - (المبلغ المدفوع × 2.75% + 3) = المطلوب
        # المبلغ المدفوع × (1 - 0.0275) - 3 = المطلوب
        # المبلغ المدفوع = (المطلوب + 3) ÷ 0.9725
        total_price = round((required_net_amount + 3) / 0.9725, 2)

        # حساب رسوم Paymob الفعلية
        paymob_fee = round(total_price - required_net_amount, 2)

        amount_cents = int(total_price * 100)
        currency = "EGP"

        # Validate payment method
        valid_methods = ["vodafone_cash", "orange_money", "we_cash", "etisalat_cash"]
        if payment_method not in valid_methods:
            logger.error(f"Invalid payment method: {payment_method}")
            return Response({"error": "طريقة الدفع غير مدعومة"}, status=400)

        # Validate phone number
        if not phone_number:
            logger.error("Phone number is required for Mobile Wallet payment")
            return Response({"error": "رقم الهاتف مطلوب للدفع عبر المحفظة"}, status=400)

        # 1. Get authentication token
        auth_response = requests.post(
            f"{settings.PAYMOB_BASE_URL}auth/tokens",
            json={"api_key": settings.PAYMOB_API_KEY},
        )
        auth_json = auth_response.json()
        token = auth_json.get("token")
        if not token:
            logger.error(f"Auth token error: {auth_response.text}")
            return Response(
                {"error": "فشل المصادقة مع Paymob", "details": auth_response.text},
                status=400,
            )

        # 2. Create order
        order_response = requests.post(
            f"{settings.PAYMOB_BASE_URL}ecommerce/orders",
            json={
                "auth_token": token,
                "delivery_needed": False,
                "amount_cents": amount_cents,
                "currency": currency,
                "items": [
                    {
                        "name": course.title,
                        "amount_cents": amount_cents,
                        "description": f"Course: {course.title}",
                        "quantity": 1,
                    }
                ],
            },
        )
        if order_response.status_code != 201:
            logger.error(f"Order creation error: {order_response.text}")
            return Response({"error": "فشل إنشاء الطلب"}, status=400)
        order_id = order_response.json()["id"]

        # 3. Create payment key
        payment_key_response = requests.post(
            f"{settings.PAYMOB_BASE_URL}acceptance/payment_keys",
            json={
                "auth_token": token,
                "amount_cents": amount_cents,
                "expiration": 3600,
                "order_id": order_id,
                "billing_data": {
                    "email": request.user.email or "<EMAIL>",
                    "first_name": request.user.first_name or "Student",
                    "last_name": request.user.last_name or "User",
                    "phone_number": phone_number,
                    "apartment": "NA",
                    "floor": "NA",
                    "street": "NA",
                    "building": "NA",
                    "city": "NA",
                    "country": "NA",
                    "postal_code": "NA",
                    "state": "NA",
                },
                "currency": currency,
                "integration_id": settings.PAYMOB_INTEGRATION_ID,
                "callback_url": f"{settings.FRONTEND_URL}/api/paymob-webhook/",
                "return_url": f"{settings.FRONTEND_URL}/course/{course_id}/success?close=true",
                "source": {"identifier": payment_method, "subtype": "WALLET"},
            },
        )
        if payment_key_response.status_code != 201:
            logger.error(f"Payment key error: {payment_key_response.text}")
            return Response({"error": "فشل إنشاء مفتاح الدفع"}, status=400)
        payment_key = payment_key_response.json()["token"]

        # 4. Save payment session (مؤقت حتى ينجح الدفع)
        from django.utils import timezone
        from datetime import timedelta
        from .models import PaymentSession

        payment_session = PaymentSession.objects.create(
            user=request.user,
            course=course,
            amount=total_price,  # المبلغ الذي دفعه الطالب
            paymob_order_id=order_id,
            payment_method=payment_method,
            phone_number=phone_number,
            base_price=base_price,  # السعر الأصلي للكورس (بعد الخصم)
            paymob_fee=paymob_fee,  # رسوم Paymob
            platform_fee=platform_fee,  # عمولة المنصة (5%)
            billing_email=request.user.email,
            billing_name=f"{request.user.first_name} {request.user.last_name}".strip(),
            expires_at=timezone.now() + timedelta(hours=1),  # انتهاء صلاحية بعد ساعة
        )

        return Response(
            {
                "payment_key": payment_key,
                "iframe_id": settings.PAYMOB_IFRAME_ID,
                "total_price": total_price,
                "paymob_fee": paymob_fee,
                "platform_fee": platform_fee,
                "base_price": base_price,
            }
        )
    except Exception as e:
        logger.error(f"Payment intent error: {str(e)}")
        return Response({"error": str(e)}, status=400)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def payment_status_by_course(request, course_id):
    """
    التحقق من حالة الدفع للكورس - zaki alkholy
    """
    try:
        # البحث عن آخر طلب للمستخدم الحالي للكورس المحدد
        order = Order.objects.filter(
            user=request.user,
            course_id=course_id
        ).order_by('-created_at').first()

        if not order:
            return Response({"status": "not_found"}, status=404)

        return Response({"status": order.status}, status=200)

    except Exception as e:
        logger.error(f"Payment status check error: {str(e)}")
        return Response({"error": str(e)}, status=400)


@api_view(["GET", "POST"])
@permission_classes([AllowAny])
def paymob_webhook(request):
    # تعديل بواسطة Zaki Alkholy: تسجيل كل Webhook يصلك في ملف لسهولة التتبع
    webhook_logger.info(f"Webhook received: {request.GET.dict()}")
    try:
        if not verify_hmac(request):
            logger.error("Invalid HMAC signature")
            return Response({"error": "توقيع HMAC غير صالح"}, status=403)

        data = request.GET
        order_id = data.get("order")
        success = data.get("success") == "true"
        pending = data.get("pending") == "true"

        # البحث عن Payment Session أولاً
        from .models import PaymentSession, Order

        try:
            payment_session = PaymentSession.objects.get(paymob_order_id=order_id)
        except PaymentSession.DoesNotExist:
            # إذا لم توجد payment session، ابحث عن order موجود (للتوافق مع النظام القديم)
            try:
                order = Order.objects.get(paymob_order_id=order_id)
                if order.status == "completed":
                    return Response({"status": "already_handled"})
            except Order.DoesNotExist:
                logger.error(f"Payment session or order not found: {order_id}")
                return Response({"error": "جلسة الدفع غير موجودة"}, status=404)

        # التحقق من انتهاء صلاحية الجلسة
        if 'payment_session' in locals() and payment_session.is_expired():
            logger.error(f"Payment session expired: {order_id}")
            return Response({"error": "انتهت صلاحية جلسة الدفع"}, status=400)

        if success:
            # إنشاء Order حقيقي من Payment Session
            if 'payment_session' in locals():
                # إنشاء Order جديد من Payment Session
                order = Order.objects.create(
                    user=payment_session.user,
                    course=payment_session.course,
                    amount=payment_session.amount,  # المبلغ الذي دفعه الطالب
                    status="completed",
                    paymob_order_id=payment_session.paymob_order_id,
                    base_price=payment_session.base_price,  # السعر الأصلي (بعد الخصم)
                    paymob_fee=payment_session.paymob_fee,  # رسوم Paymob
                    platform_fee=payment_session.platform_fee,  # عمولة المنصة (5%)
                    billing_email=payment_session.billing_email,
                    billing_name=payment_session.billing_name,
                )

                # إنشاء Payment record
                from .models import Payment
                payment_record = Payment.objects.create(
                    order=order,
                    amount=payment_session.amount,  # المبلغ المدفوع
                    payment_method=payment_session.payment_method,
                    transaction_id=str(order_id),  # Paymob order ID
                    status="completed",
                    payment_details={
                        'paymob_order_id': str(order_id),
                        'base_price': str(payment_session.base_price),
                        'paymob_fee': str(payment_session.paymob_fee),
                        'platform_fee': str(payment_session.platform_fee),
                        'phone_number': payment_session.phone_number,
                        'payment_method': payment_session.payment_method,
                        'webhook_data': request.GET.dict(),
                    }
                )

                # حذف Payment Session بعد إنشاء Order و Payment
                payment_session.delete()

                logger.info(f"Order {order.id} and Payment {payment_record.id} created successfully from payment session {order_id}")
            else:
                # النظام القديم - تحديث Order موجود
                order.status = "completed"
                order.save()

            # تفعيل الكورس للطالب
            if order.user and order.course:
                from .models import Enrollment  # , InstructorPayout  # تم تعطيل InstructorPayout مؤقتاً - zaki alkholy

                enrollment, created = Enrollment.objects.get_or_create(
                    student=order.user, course=order.course
                )
                if order.user not in order.course.students.all():
                    order.course.students.add(order.user)
                    order.course.save()
                if created:
                    logger.info(
                        f"Student {order.user.id} enrolled in course {order.course.id} after payment."
                    )

            # =============================
            # نظام تحويل الأموال للمعلم تلقائياً بعد دفع الطالب - zaki alkholy
            # تم تعطيل هذا النظام مؤقتاً حسب طلب المطور
            # =============================

            # حساب عمولة المنصة وصافي ربح المعلم
            # base_price = float(order.course.price)
            # platform_fee = round(base_price * 0.05, 2)
            # instructor_amount = round(base_price - platform_fee, 2)
            # payout_status = "success"
            # payout_error = None
            # payout_response = None
            # try:
            #     # استدعاء التحويل الفعلي عبر Paymob
            #     payout_response = process_payout(
            #         order.course.instructor.instructor_profile, instructor_amount
            #     )
            # except Exception as e:
            #     payout_status = "failed"
            #     payout_error = str(e)
            #     logger.error(f"Failed to process payout for instructor: {payout_error}")
            # try:
            #     InstructorPayout.objects.create(
            #         instructor=order.course.instructor.instructor_profile,
            #         order=order,
            #         course=order.course,
            #         amount_paid=instructor_amount,
            #         platform_fee=platform_fee,
            #         status=payout_status,
            #         error_message=payout_error,
            #     )
            # except Exception as e:
            #     logger.error(f"Failed to create InstructorPayout: {str(e)}")

            # if payout_status == "failed":
            #     return Response(
            #         {
            #             "status": "success",
            #             "warning": "تم الدفع للطالب لكن فشل تحويل أرباح المعلم",
            #             "details": payout_error,
            #         }
            #     )

            # =============================
            # نهاية نظام تحويل الأموال للمعلم - zaki alkholy
            # =============================
            return Response({"status": "success"})

        elif pending:
            # في حالة pending، نبقي Payment Session كما هي
            logger.info(f"Payment pending for session {order_id}")
        else:
            # في حالة فشل الدفع، نحذف Payment Session
            if 'payment_session' in locals():
                payment_session.delete()
                logger.info(f"Payment session {order_id} deleted due to failed payment")
            elif 'order' in locals():
                # النظام القديم
                order.status = "failed"
                order.save()

        return Response({"status": "success"})
    except Exception as e:
        logger.error(f"Webhook error: {str(e)}")
        return Response({"error": str(e)}, status=400)


# =============================
# دالة تحويل الأموال للمعلم تلقائياً - zaki alkholy
# تم تعطيل هذه الدالة مؤقتاً حسب طلب المطور
# =============================

# def process_payout(instructor_profile, amount, wallet_number=None):
#     """
#     تحويل أرباح المعلم عبر Paymob Payout API (staging)
#     - يحصل على OAuth token
#     - ينفذ التحويل (Instant Cashin API)
#     - يسجل أي أخطاء في الـ logging
#     """
#     logger = logging.getLogger(__name__)
#     try:
#         from requests.auth import HTTPBasicAuth

#         # إعداد بيانات البيئة
#         PAYOUT_BASE_URL = getattr(
#             settings, "PAYMOB_PAYOUT_BASE_URL", os.environ.get("PAYMOB_PAYOUT_BASE_URL")
#         )
#         CLIENT_ID = getattr(
#             settings,
#             "PAYMOB_PAYOUT_CLIENT_ID",
#             os.environ.get("PAYMOB_PAYOUT_CLIENT_ID"),
#         )
#         CLIENT_SECRET = getattr(
#             settings,
#             "PAYMOB_PAYOUT_CLIENT_SECRET",
#             os.environ.get("PAYMOB_PAYOUT_CLIENT_SECRET"),
#         )
#         USERNAME = getattr(
#             settings, "PAYMOB_PAYOUT_USERNAME", os.environ.get("PAYMOB_PAYOUT_USERNAME")
#         )
#         PASSWORD = getattr(
#             settings, "PAYMOB_PAYOUT_PASSWORD", os.environ.get("PAYMOB_PAYOUT_PASSWORD")
#         )

#         # طباعة Debug للمتغيرات
#         logger.debug(f"[PAYOUT DEBUG] BASE_URL: {PAYOUT_BASE_URL}")
#         logger.debug(f"[PAYOUT DEBUG] CLIENT_ID: {CLIENT_ID}")
#         logger.debug(f"[PAYOUT DEBUG] CLIENT_SECRET: {CLIENT_SECRET}")
#         logger.debug(f"[PAYOUT DEBUG] USERNAME: {USERNAME}")
#         logger.debug(
#             f"[PAYOUT DEBUG] PASSWORD موجود: {bool(PASSWORD)} وطوله: {len(PASSWORD) if PASSWORD else 0}"
#         )

#         # تحقق من وجود جميع المتغيرات
#         if not all([PAYOUT_BASE_URL, CLIENT_ID, CLIENT_SECRET, USERNAME, PASSWORD]):
#             raise Exception(
#                 f"بيانات إعدادات Paymob Payout ناقصة: PAYOUT_BASE_URL={PAYOUT_BASE_URL}, CLIENT_ID={CLIENT_ID}, CLIENT_SECRET={CLIENT_SECRET}, USERNAME={USERNAME}, PASSWORD={PASSWORD}"
#             )
#         if PASSWORD is not None:
#             PASSWORD = str(PASSWORD).strip('"')
#         else:
#             raise Exception("PAYMOB_PAYOUT_PASSWORD غير معرف في الإعدادات أو .env")

#         # إزالة أي / زائد من نهاية الرابط
#         base_url = PAYOUT_BASE_URL.rstrip("/")

#         # 1. الحصول على OAuth token (حسب توثيق Paymob)
#         token_url = f"{base_url}/api/secure/o/token/"
#         token_data = {
#             "grant_type": "password",
#             "username": USERNAME,
#             "password": PASSWORD,
#         }
#         headers = {"Content-Type": "application/x-www-form-urlencoded"}
#         auth = HTTPBasicAuth(CLIENT_ID, CLIENT_SECRET)
#         logger.debug(f"[PAYOUT DEBUG] token_url: {token_url}")
#         logger.debug(f"[PAYOUT DEBUG] token_data: {token_data}")
#         token_resp = requests.post(
#             token_url, data=token_data, headers=headers, auth=auth
#         )
#         logger.info(
#             f"Paymob payout token response: {token_resp.status_code} {token_resp.text}"
#         )
#         if token_resp.status_code != 200:
#             raise Exception(f"فشل الحصول على OAuth token: {token_resp.text}")
#         access_token = token_resp.json().get("access_token")
#         if not access_token:
#             raise Exception("لم يتم الحصول على access_token من Paymob")

#         # 2. تنفيذ التحويل (Instant Cashin API)
#         if not wallet_number:
#             payment_details = instructor_profile.payment_details or {}
#             if isinstance(payment_details, str):
#                 import json

#                 payment_details = json.loads(payment_details)
#             wallet_number = payment_details.get("wallet_number")
#             if not wallet_number:
#                 # fallback: check User.wallet_number
#                 user = getattr(instructor_profile, "user", None)
#                 if user:
#                     wallet_number = getattr(user, "wallet_number", None)
#         if not wallet_number:
#             raise Exception("رقم محفظة المعلم (wallet_number) غير متوفر")
#         # تحديد issuer بناءً على رقم المحفظة
#         issuer = None
#         if wallet_number.startswith("010"):
#             issuer = "vodafone"
#         elif wallet_number.startswith("011"):
#             issuer = "etisalat"
#         elif wallet_number.startswith("012"):
#             issuer = "orange"
#         elif wallet_number.startswith("015"):
#             issuer = "we"
#         else:
#             raise Exception("رقم المحفظة غير مدعوم للتحويل التلقائي")
#         disburse_url = f"{base_url}/api/secure/disburse/"

#         disburse_data = {
#             "amount": str(amount),
#             "issuer": issuer,
#             "msisdn": wallet_number,
#         }
#         disburse_headers = {
#             "Content-Type": "application/json",
#             "Authorization": f"Bearer {access_token}",
#         }
#         logger.debug(f"[PAYOUT DEBUG] disburse_url: {disburse_url}")
#         logger.debug(f"[PAYOUT DEBUG] disburse_data: {disburse_data}")
#         disburse_resp = requests.post(
#             disburse_url, json=disburse_data, headers=disburse_headers
#         )
#         logger.info(
#             f"Paymob payout disburse response: {disburse_resp.status_code} {disburse_resp.text}"
#         )
#         if disburse_resp.status_code != 200:
#             raise Exception(f"فشل التحويل: {disburse_resp.text}")
#         return disburse_resp.json()
#     except Exception as e:
#         logger.error(f"Payout processing error: {str(e)}")
#         raise

# =============================
# نهاية دالة تحويل الأموال للمعلم - zaki alkholy
# =============================
