from django.contrib.auth import get_user_model
from rest_framework import serializers
import html
import re

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    payment_details = serializers.JSONField(required=False, allow_null=True)

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "password",
            "phone_number",
            "profile_image",
            "bio",
            "date_of_birth",
            "language",
            "is_instructor",
            "is_student",
            "is_superuser",
            "is_staff",
            "wallet_number",
            "payment_method",
            "payment_details",
            "first_name",
            "last_name",
            "has_wallet",
            "email_verified",
        ]

        read_only_fields = ["id", "has_wallet", "is_superuser", "is_staff"]
        extra_kwargs = {
            "password": {"write_only": True},
            "phone_number": {"required": False},
            "profile_image": {"required": False},
            "wallet_number": {"required": False},
            "payment_method": {"required": False},
            "payment_details": {"required": False},
        }

    def _sanitize_input(self, value):
        """تنظيف المدخلات من XSS والمحتوى الخطير - zaki alkholy"""
        if not value:
            return value

        # إزالة HTML tags
        value = html.escape(str(value))

        # إزالة JavaScript والمحتوى الخطير
        dangerous_patterns = [
            r'<script.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'<iframe.*?</iframe>',
            r'<object.*?</object>',
            r'<embed.*?</embed>',
        ]

        for pattern in dangerous_patterns:
            value = re.sub(pattern, '', value, flags=re.IGNORECASE | re.DOTALL)

        return value.strip()

    def validate_first_name(self, value):
        """تنظيف الاسم الأول - zaki alkholy"""
        return self._sanitize_input(value)

    def validate_last_name(self, value):
        """تنظيف الاسم الأخير - zaki alkholy"""
        return self._sanitize_input(value)

    def validate_bio(self, value):
        """تنظيف السيرة الذاتية - zaki alkholy"""
        return self._sanitize_input(value)

    def validate_email(self, value):
        user = self.instance
        User = get_user_model()
        if (
            User.objects.filter(email=value)
            .exclude(pk=user.pk if user else None)
            .exists()
        ):
            raise serializers.ValidationError("البريد الإلكتروني مستخدم بالفعل.")
        return value

    def create(self, validated_data):
        password = validated_data.pop("password")
        user = User(**validated_data)
        user.set_password(password)
        user.save()
        return user

    def validate(self, data):
        if data.get("is_instructor"):
            if data.get("payment_method") and not data.get("wallet_number"):
                raise serializers.ValidationError(
                    "يجب إدخال رقم المحفظة مع وسيلة الدفع."
                )
            if data.get("wallet_number") and not data.get("payment_method"):
                raise serializers.ValidationError(
                    "يجب اختيار وسيلة الدفع مع رقم المحفظة."
                )
        return data

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if not instance.is_instructor:
            ret.pop("wallet_number", None)
            ret.pop("payment_method", None)
        else:
            if instance.wallet_number and not self.context["request"].user.is_staff:
                ret["wallet_number"] = instance.wallet_number
                self.fields["wallet_number"].read_only = True
            if instance.payment_method and not self.context["request"].user.is_staff:
                ret["payment_method"] = instance.payment_method
                self.fields["payment_method"].read_only = True
        return ret
