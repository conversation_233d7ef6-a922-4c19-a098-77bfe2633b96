# دليل استكشاف أخطاء Bunny Stream - زكي الخولي

## المشكلة الحالية: خطأ 401 Unauthorized

### السبب
API Key الخاص بـ Bunny Stream غير صحيح أو منتهي الصلاحية.

### الحل المطلوب

#### 1. الحصول على API Key صحيح
1. اذهب إلى [Bunny Stream Dashboard](https://dash.bunny.net/)
2. سجل دخول إلى حسابك
3. اذهب إلى **Stream** → **API Keys**
4. انسخ الـ **Stream API Key** (ليس Pull Zone API Key)

#### 2. تحديث الإعدادات
```bash
# في ملف .env
BUNNY_STREAM_API_KEY=YOUR_CORRECT_API_KEY_HERE
```

#### 3. التحقق من Library ID
1. في Bunny Dashboard، اذهب إلى **Stream** → **Video Libraries**
2. انقر على المكتبة المطلوبة
3. انسخ الـ **Library ID** من الـ URL أو من الإعدادات

#### 4. اختبار الاتصال
```bash
cd BackEnd/Newmnasa
python test_bunny_api.py
```

## أخطاء شائعة أخرى

### خطأ 403 Forbidden
- **السبب**: ليس لديك صلاحية للوصول لهذه المكتبة
- **الحل**: تأكد من أن API Key يملك صلاحيات على المكتبة المحددة

### خطأ 404 Not Found
- **السبب**: Library ID غير صحيح
- **الحل**: تحقق من Library ID في Dashboard

### خطأ 429 Too Many Requests
- **السبب**: تجاوز حد الطلبات المسموح
- **الحل**: انتظر قليلاً قبل إعادة المحاولة

### خطأ 500 Internal Server Error
- **السبب**: مشكلة في خوادم Bunny
- **الحل**: انتظر وأعد المحاولة لاحقاً

## إعدادات مطلوبة في .env

```bash
# Bunny Stream Configuration
BUNNY_STREAM_API_KEY=your-stream-api-key-here
BUNNY_STREAM_LIBRARY_ID=your-library-id
BUNNY_STREAM_CDN_HOSTNAME=your-cdn-hostname.b-cdn.net
```

## كيفية الحصول على CDN Hostname
1. في Bunny Dashboard → Stream → Video Libraries
2. انقر على المكتبة
3. اذهب إلى **Settings** → **Delivery**
4. انسخ الـ **CDN Hostname**

## اختبار سريع للإعدادات

```python
import requests

api_key = "your-api-key"
library_id = "your-library-id"

headers = {
    "AccessKey": api_key,
    "Content-Type": "application/json"
}

# اختبار الاتصال
response = requests.get(
    f"https://video.bunnycdn.com/library/{library_id}",
    headers=headers
)

print(f"Status: {response.status_code}")
print(f"Response: {response.text}")
```

## نصائح مهمة

1. **API Key مختلف عن Pull Zone API Key**
   - استخدم Stream API Key وليس Pull Zone API Key

2. **تحقق من الصلاحيات**
   - تأكد من أن API Key له صلاحيات على المكتبة

3. **احفظ نسخة احتياطية**
   - احفظ API Key في مكان آمن

4. **مراقبة الاستخدام**
   - تابع استخدامك في Dashboard لتجنب تجاوز الحدود

## جهات الاتصال للدعم

- **Bunny Support**: <EMAIL>
- **Documentation**: https://docs.bunny.net/
- **Community**: https://community.bunny.net/

---
**آخر تحديث**: 23 يوليو 2025 - زكي الخولي
