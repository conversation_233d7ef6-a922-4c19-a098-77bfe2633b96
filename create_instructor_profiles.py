from main.models import User, InstructorProfile

def create_missing_instructor_profiles():
    count = 0
    for user in User.objects.filter(is_instructor=True):
        if not hasattr(user, 'instructor_profile'):
            InstructorProfile.objects.create(user=user, specialization='', qualifications='')
            print(f"Created InstructorProfile for: {user.username}")
            count += 1
    print(f"Done. Created {count} missing InstructorProfiles.")

if __name__ == "__main__":
    create_missing_instructor_profiles()
