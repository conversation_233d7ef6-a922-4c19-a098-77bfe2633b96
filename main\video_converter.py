import ffmpeg
import os
import tempfile


class VideoConverter:
    @staticmethod
    def convert_to_hls(input_path, output_dir=None):
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"File not found: {input_path}")

        if not output_dir:
            output_dir = tempfile.mkdtemp()

        output_path = os.path.join(output_dir, "output.m3u8")

        try:
            (
                ffmpeg.input(input_path)
                .output(
                    output_path,
                    format="hls",
                    start_number=0,
                    hls_time=10,
                    hls_list_size=0,
                    hls_segment_filename=os.path.join(output_dir, "segment_%03d.ts"),
                    vcodec="libx264",
                    acodec="aac",
                )
                .run(overwrite_output=True)
            )
            return output_path, output_dir  # output.m3u8 path + folder that has .ts
        except ffmpeg.Error as e:
            raise RuntimeError(f"HLS conversion failed: {e.stderr.decode()}")
