# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

# Django imports
from django.db.models import Count, Avg, Sum, Q, F, FloatField
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model

# Models
from ..models import (
    Course,
    Lesson,
    StudentProgress,
    StudentPoints,
    UserQuizAttempt,
    Order,
    Assignment,
    AssignmentSubmission,
    LessonAnalytics,
    Review,
    Enrollment,
)

# Serializers
from ..serializers_file.progress_serializer import (
    StudentProgressSerializer,
    LessonAnalyticsSerializer,
)
from ..serializers_file.assignment_serializer import (
    InstructorAssignmentStatsSerializer,
)
from ..serializers_file.course_serializer import CourseReadSerializer

User = get_user_model()


class InstructorAnalyticsAPIView(APIView):
    """API للتحليلات والتقارير للمعلمين"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على تحليلات شاملة للمعلم"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        instructor = request.user
        
        # الحصول على دورات المعلم
        courses = Course.objects.filter(instructor=instructor)
        
        # إحصائيات عامة
        total_courses = courses.count()
        published_courses = courses.filter(is_published=True).count()
        
        # إحصائيات الطلاب
        total_enrollments = Enrollment.objects.filter(course__instructor=instructor).count()
        active_students = Enrollment.objects.filter(
            course__instructor=instructor,
            last_accessed__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        # إحصائيات الإيرادات
        completed_orders = Order.objects.filter(
            course__instructor=instructor,
            status="completed"
        )
        total_revenue = completed_orders.aggregate(Sum('amount'))['amount__sum'] or 0
        monthly_revenue = completed_orders.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).aggregate(Sum('amount'))['amount__sum'] or 0
        
        # إحصائيات الاختبارات
        quiz_attempts = UserQuizAttempt.objects.filter(
            quiz__lesson__course__instructor=instructor
        )
        total_quiz_attempts = quiz_attempts.count()
        passed_attempts = quiz_attempts.filter(passed=True).count()
        quiz_pass_rate = (passed_attempts / total_quiz_attempts * 100) if total_quiz_attempts > 0 else 0
        
        # إحصائيات التقييمات
        reviews = Review.objects.filter(course__instructor=instructor, is_approved=True)
        average_rating = reviews.aggregate(Avg('rating'))['rating__avg'] or 0
        total_reviews = reviews.count()
        
        return Response({
            "overview": {
                "total_courses": total_courses,
                "published_courses": published_courses,
                "total_students": total_enrollments,
                "active_students": active_students,
                "total_revenue": float(total_revenue),
                "monthly_revenue": float(monthly_revenue),
                "average_rating": round(average_rating, 2),
                "total_reviews": total_reviews,
                "quiz_pass_rate": round(quiz_pass_rate, 2),
            }
        })


class CourseAnalyticsAPIView(APIView):
    """تحليلات مفصلة لدورة معينة"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, course_id):
        """الحصول على تحليلات دورة محددة"""
        try:
            course = Course.objects.get(id=course_id, instructor=request.user)
        except Course.DoesNotExist:
            return Response(
                {"error": "الدورة غير موجودة أو ليس لديك صلاحية للوصول إليها"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # إحصائيات الطلاب
        enrollments = Enrollment.objects.filter(course=course)
        total_students = enrollments.count()
        completed_students = enrollments.filter(completed=True).count()
        active_students = enrollments.filter(
            last_accessed__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        # تقدم الطلاب
        progress_data = StudentProgress.objects.filter(course=course).aggregate(
            avg_completion=Avg('completion_percentage'),
            total_watch_time=Sum('watch_time')
        )
        
        # إحصائيات الدروس
        lessons = course.lessons.all()
        lesson_stats = []
        
        for lesson in lessons:
            lesson_progress = StudentProgress.objects.filter(lesson=lesson)
            completed_count = lesson_progress.filter(status='completed').count()
            avg_watch_time = lesson_progress.aggregate(Avg('watch_time'))['watch_time__avg'] or 0
            
            lesson_stats.append({
                "lesson_id": str(lesson.id),
                "lesson_title": lesson.title,
                "total_viewers": lesson_progress.count(),
                "completed_viewers": completed_count,
                "completion_rate": (completed_count / total_students * 100) if total_students > 0 else 0,
                "average_watch_time": round(avg_watch_time / 60, 2),  # بالدقائق
            })
        
        # إحصائيات الاختبارات
        quiz_attempts = UserQuizAttempt.objects.filter(quiz__lesson__course=course)
        quiz_stats = {
            "total_attempts": quiz_attempts.count(),
            "passed_attempts": quiz_attempts.filter(passed=True).count(),
            "average_score": quiz_attempts.aggregate(Avg('score'))['score__avg'] or 0,
        }
        
        # إحصائيات الإيرادات
        orders = Order.objects.filter(course=course, status="completed")
        revenue_stats = {
            "total_orders": orders.count(),
            "total_revenue": float(orders.aggregate(Sum('amount'))['amount__sum'] or 0),
            "average_order_value": float(orders.aggregate(Avg('amount'))['amount__avg'] or 0),
        }
        
        # التقييمات
        reviews = Review.objects.filter(course=course, is_approved=True)
        review_stats = {
            "total_reviews": reviews.count(),
            "average_rating": reviews.aggregate(Avg('rating'))['rating__avg'] or 0,
            "rating_distribution": {
                str(i): reviews.filter(rating=i).count() for i in range(1, 6)
            }
        }
        
        return Response({
            "course_info": {
                "id": str(course.id),
                "title": course.title,
                "created_at": course.created_at,
                "is_published": course.is_published,
            },
            "student_stats": {
                "total_students": total_students,
                "completed_students": completed_students,
                "active_students": active_students,
                "completion_rate": (completed_students / total_students * 100) if total_students > 0 else 0,
                "average_progress": round(progress_data['avg_completion'] or 0, 2),
                "total_watch_hours": round((progress_data['total_watch_time'] or 0) / 3600, 2),
            },
            "lesson_stats": lesson_stats,
            "quiz_stats": quiz_stats,
            "revenue_stats": revenue_stats,
            "review_stats": review_stats,
        })


class StudentPerformanceAPIView(APIView):
    """تقرير أداء الطلاب المفصل"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, course_id):
        """الحصول على تقرير أداء الطلاب في دورة"""
        try:
            course = Course.objects.get(id=course_id, instructor=request.user)
        except Course.DoesNotExist:
            return Response(
                {"error": "الدورة غير موجودة أو ليس لديك صلاحية للوصول إليها"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # الحصول على جميع الطلاب المسجلين
        enrollments = Enrollment.objects.filter(course=course).select_related('student')
        
        student_reports = []
        
        for enrollment in enrollments:
            student = enrollment.student
            
            # تقدم الطالب في الدروس
            lesson_progress = StudentProgress.objects.filter(
                student=student,
                course=course
            )
            
            completed_lessons = lesson_progress.filter(status='completed').count()
            total_lessons = course.lessons.count()
            
            # أداء الطالب في الاختبارات
            quiz_attempts = UserQuizAttempt.objects.filter(
                user=student,
                quiz__lesson__course=course
            )
            
            passed_quizzes = quiz_attempts.filter(passed=True).count()
            total_quizzes = quiz_attempts.values('quiz').distinct().count()
            avg_quiz_score = quiz_attempts.aggregate(Avg('score'))['score__avg'] or 0
            
            # الواجبات
            assignment_submissions = AssignmentSubmission.objects.filter(
                student=student,
                assignment__lesson__course=course,
                status='graded'
            )
            
            avg_assignment_score = assignment_submissions.aggregate(Avg('score'))['score__avg'] or 0
            
            # وقت المشاهدة
            total_watch_time = lesson_progress.aggregate(Sum('watch_time'))['watch_time__sum'] or 0
            
            # آخر نشاط
            last_activity = enrollment.last_accessed
            
            student_reports.append({
                "student_id": str(student.id),
                "student_name": student.username,
                "student_email": student.email,
                "enrollment_date": enrollment.enrolled_at,
                "last_activity": last_activity,
                "progress": {
                    "completed_lessons": completed_lessons,
                    "total_lessons": total_lessons,
                    "completion_percentage": (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0,
                    "total_watch_hours": round(total_watch_time / 3600, 2),
                },
                "quiz_performance": {
                    "passed_quizzes": passed_quizzes,
                    "total_quizzes": total_quizzes,
                    "pass_rate": (passed_quizzes / total_quizzes * 100) if total_quizzes > 0 else 0,
                    "average_score": round(avg_quiz_score, 2),
                },
                "assignment_performance": {
                    "submitted_assignments": assignment_submissions.count(),
                    "average_score": round(avg_assignment_score, 2),
                },
                "overall_status": "active" if last_activity and last_activity >= timezone.now() - timedelta(days=7) else "inactive",
            })
        
        # ترتيب الطلاب حسب التقدم
        student_reports.sort(key=lambda x: x['progress']['completion_percentage'], reverse=True)
        
        return Response({
            "course_title": course.title,
            "total_students": len(student_reports),
            "students": student_reports,
        })


class SalesAnalyticsAPIView(APIView):
    """تحليلات المبيعات للمعلم"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على تحليلات المبيعات"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        instructor = request.user
        period = request.query_params.get('period', '30')  # آخر 30 يوم افتراضياً
        
        try:
            days = int(period)
        except ValueError:
            days = 30
        
        start_date = timezone.now() - timedelta(days=days)
        
        # طلبات المعلم
        orders = Order.objects.filter(
            course__instructor=instructor,
            status="completed",
            created_at__gte=start_date
        )
        
        # إحصائيات عامة
        total_sales = orders.count()
        total_revenue = orders.aggregate(Sum('amount'))['amount__sum'] or 0
        average_order_value = orders.aggregate(Avg('amount'))['amount__avg'] or 0
        
        # المبيعات حسب الدورة
        course_sales = orders.values('course__title', 'course__id').annotate(
            sales_count=Count('id'),
            revenue=Sum('amount')
        ).order_by('-revenue')
        
        # المبيعات اليومية
        daily_sales = []
        for i in range(days):
            date = (timezone.now() - timedelta(days=i)).date()
            day_orders = orders.filter(created_at__date=date)
            daily_sales.append({
                "date": date,
                "sales_count": day_orders.count(),
                "revenue": float(day_orders.aggregate(Sum('amount'))['amount__sum'] or 0),
            })
        
        daily_sales.reverse()  # ترتيب من الأقدم للأحدث
        
        return Response({
            "period_days": days,
            "summary": {
                "total_sales": total_sales,
                "total_revenue": float(total_revenue),
                "average_order_value": float(average_order_value),
            },
            "course_sales": [
                {
                    "course_id": str(item['course__id']),
                    "course_title": item['course__title'],
                    "sales_count": item['sales_count'],
                    "revenue": float(item['revenue']),
                }
                for item in course_sales
            ],
            "daily_sales": daily_sales,
        })


class LessonAnalyticsDetailAPIView(APIView):
    """تحليلات مفصلة لدرس معين"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, lesson_id):
        """الحصول على تحليلات درس محدد"""
        try:
            lesson = Lesson.objects.get(id=lesson_id, course__instructor=request.user)
        except Lesson.DoesNotExist:
            return Response(
                {"error": "الدرس غير موجود أو ليس لديك صلاحية للوصول إليه"},
                status=status.HTTP_404_NOT_FOUND
            )

        # إحصائيات المشاهدة
        progress_records = StudentProgress.objects.filter(lesson=lesson)

        total_viewers = progress_records.count()
        completed_viewers = progress_records.filter(status='completed').count()
        avg_completion = progress_records.aggregate(Avg('completion_percentage'))['completion_percentage__avg'] or 0
        avg_watch_time = progress_records.aggregate(Avg('watch_time'))['watch_time__avg'] or 0

        # نقاط التوقف الشائعة (تحليل أين يتوقف الطلاب)
        drop_points = []
        if lesson.duration > 0:
            # تقسيم الدرس إلى 10 أجزاء وحساب معدل التوقف في كل جزء
            segment_duration = lesson.duration * 60 / 10  # تحويل لثواني

            for i in range(10):
                start_time = i * segment_duration
                end_time = (i + 1) * segment_duration

                # الطلاب الذين توقفوا في هذا الجزء
                stopped_in_segment = progress_records.filter(
                    watch_time__gte=start_time,
                    watch_time__lt=end_time,
                    status__in=['not_started', 'in_progress']
                ).count()

                drop_points.append({
                    "segment": i + 1,
                    "start_time": int(start_time),
                    "end_time": int(end_time),
                    "drop_count": stopped_in_segment,
                    "drop_rate": (stopped_in_segment / total_viewers * 100) if total_viewers > 0 else 0,
                })

        # أداء الاختبارات المرتبطة بالدرس
        quiz_performance = []
        for quiz in lesson.quizzes.all():
            attempts = UserQuizAttempt.objects.filter(quiz=quiz)
            quiz_performance.append({
                "quiz_id": str(quiz.id),
                "quiz_title": quiz.title,
                "total_attempts": attempts.count(),
                "passed_attempts": attempts.filter(passed=True).count(),
                "average_score": attempts.aggregate(Avg('score'))['score__avg'] or 0,
                "pass_rate": (attempts.filter(passed=True).count() / attempts.count() * 100) if attempts.count() > 0 else 0,
            })

        # الواجبات المرتبطة بالدرس
        assignment_performance = []
        for assignment in lesson.assignments.all():
            submissions = AssignmentSubmission.objects.filter(assignment=assignment)
            graded_submissions = submissions.filter(status='graded')

            assignment_performance.append({
                "assignment_id": str(assignment.id),
                "assignment_title": assignment.title,
                "total_submissions": submissions.count(),
                "graded_submissions": graded_submissions.count(),
                "average_score": graded_submissions.aggregate(Avg('score'))['score__avg'] or 0,
                "on_time_submissions": submissions.filter(is_late=False).count(),
                "late_submissions": submissions.filter(is_late=True).count(),
            })

        return Response({
            "lesson_info": {
                "id": str(lesson.id),
                "title": lesson.title,
                "duration_minutes": lesson.duration,
                "lesson_type": lesson.lesson_type,
                "order": lesson.order,
            },
            "viewing_stats": {
                "total_viewers": total_viewers,
                "completed_viewers": completed_viewers,
                "completion_rate": (completed_viewers / total_viewers * 100) if total_viewers > 0 else 0,
                "average_completion_percentage": round(avg_completion, 2),
                "average_watch_time_minutes": round(avg_watch_time / 60, 2),
            },
            "engagement_analysis": {
                "drop_points": drop_points,
                "most_problematic_segment": max(drop_points, key=lambda x: x['drop_rate']) if drop_points else None,
            },
            "quiz_performance": quiz_performance,
            "assignment_performance": assignment_performance,
        })


class InstructorDashboardStatsAPIView(APIView):
    """إحصائيات سريعة للوحة تحكم المعلم"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """الحصول على إحصائيات سريعة للمعلم"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"},
                status=status.HTTP_403_FORBIDDEN
            )

        instructor = request.user

        # إحصائيات اليوم
        today = timezone.now().date()
        today_orders = Order.objects.filter(
            course__instructor=instructor,
            status="completed",
            created_at__date=today
        )

        # إحصائيات الأسبوع
        week_start = timezone.now() - timedelta(days=7)
        week_orders = Order.objects.filter(
            course__instructor=instructor,
            status="completed",
            created_at__gte=week_start
        )

        # الطلاب النشطين
        active_students = Enrollment.objects.filter(
            course__instructor=instructor,
            last_accessed__gte=timezone.now() - timedelta(days=7)
        ).count()

        # الاختبارات الجديدة
        new_quiz_attempts = UserQuizAttempt.objects.filter(
            quiz__lesson__course__instructor=instructor,
            created_at__gte=timezone.now() - timedelta(days=1)
        ).count()

        # الواجبات المطلوب تقييمها
        pending_assignments = AssignmentSubmission.objects.filter(
            assignment__lesson__course__instructor=instructor,
            status='submitted'
        ).count()

        # التقييمات الجديدة
        new_reviews = Review.objects.filter(
            course__instructor=instructor,
            created_at__gte=timezone.now() - timedelta(days=7),
            is_approved=False
        ).count()

        return Response({
            "today": {
                "sales": today_orders.count(),
                "revenue": float(today_orders.aggregate(Sum('amount'))['amount__sum'] or 0),
            },
            "this_week": {
                "sales": week_orders.count(),
                "revenue": float(week_orders.aggregate(Sum('amount'))['amount__sum'] or 0),
            },
            "students": {
                "active_this_week": active_students,
            },
            "pending_tasks": {
                "assignments_to_grade": pending_assignments,
                "reviews_to_approve": new_reviews,
            },
            "recent_activity": {
                "new_quiz_attempts": new_quiz_attempts,
            }
        })


class TopPerformingContentAPIView(APIView):
    """المحتوى الأكثر أداءً للمعلم"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """الحصول على المحتوى الأكثر أداءً"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"},
                status=status.HTTP_403_FORBIDDEN
            )

        instructor = request.user

        # أفضل الدورات من ناحية المبيعات
        top_selling_courses = Course.objects.filter(
            instructor=instructor
        ).annotate(
            sales_count=Count('orders', filter=Q(orders__status='completed')),
            revenue=Sum('orders__amount', filter=Q(orders__status='completed')),
            avg_rating=Avg('reviews__rating', filter=Q(reviews__is_approved=True))
        ).order_by('-sales_count')[:5]

        # أفضل الدروس من ناحية الإكمال
        top_lessons = Lesson.objects.filter(
            course__instructor=instructor
        ).annotate(
            completion_rate=Avg('student_progress__completion_percentage'),
            total_viewers=Count('student_progress')
        ).filter(total_viewers__gt=0).order_by('-completion_rate')[:5]

        # أفضل الاختبارات من ناحية الأداء - zaki alkholy
        top_quizzes = UserQuizAttempt.objects.filter(
            quiz__lesson__course__instructor=instructor
        ).values(
            'quiz__id', 'quiz__title', 'quiz__lesson__title'
        ).annotate(
            total_attempts=Count('id'),
            pass_rate=Avg('passed', output_field=FloatField()) * 100,
            avg_score=Avg('score')
        ).filter(total_attempts__gt=0).order_by('-pass_rate')[:5]

        return Response({
            "top_selling_courses": [
                {
                    "course_id": str(course.id),
                    "title": course.title,
                    "sales_count": course.sales_count or 0,
                    "revenue": float(course.revenue or 0),
                    "average_rating": round(course.avg_rating or 0, 2),
                }
                for course in top_selling_courses
            ],
            "top_performing_lessons": [
                {
                    "lesson_id": str(lesson.id),
                    "title": lesson.title,
                    "course_title": lesson.course.title,
                    "completion_rate": round(lesson.completion_rate or 0, 2),
                    "total_viewers": lesson.total_viewers,
                }
                for lesson in top_lessons
            ],
            "top_performing_quizzes": [
                {
                    "quiz_id": str(quiz['quiz__id']),
                    "quiz_title": quiz['quiz__title'],
                    "lesson_title": quiz['quiz__lesson__title'],
                    "total_attempts": quiz['total_attempts'],
                    "pass_rate": round(quiz['pass_rate'], 2),
                    "average_score": round(quiz['avg_score'], 2),
                }
                for quiz in top_quizzes
            ],
        })
