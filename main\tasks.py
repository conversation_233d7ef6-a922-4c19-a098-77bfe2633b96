from celery import shared_task
from .models import Notification, UserQuizAttempt, Quiz, Answer
from .constants.notification_types import SYSTEM
from django.utils import timezone
import logging


@shared_task
def send_bulk_notification(user_ids, message, type=SYSTEM):
    logging.info(
        f"Sending bulk notification to users: {user_ids} with message: {message}"
    )
    notifications = [
        Notification(user_id=user_id, message=message, type=type)
        for user_id in user_ids
    ]
    Notification.objects.bulk_create(notifications)


@shared_task
def auto_submit_expired_exams():
    """مهمة دورية للتحقق من انتهاء وقت الامتحانات والتسليم التلقائي - zaki alkholy"""
    now = timezone.now()

    # جلب جميع المحاولات غير المسلمة للامتحانات فقط - zaki alkholy
    active_attempts = UserQuizAttempt.objects.filter(
        submitted=False, quiz__quiz_type="exam"  # الامتحانات فقط، ليس الواجبات
    )

    for attempt in active_attempts:
        quiz = attempt.quiz
        if quiz.time_limit > 0:  # فقط الامتحانات التي لها وقت محدد
            time_limit = quiz.time_limit * 60  # تحويل إلى ثواني
            elapsed = (now - attempt.created_at).total_seconds()

            if elapsed >= time_limit:
                # انتهى الوقت، قم بالتسليم التلقائي - zaki alkholy
                _auto_submit_quiz_task(attempt, quiz)
                logging.info(
                    f"zaki alkholy - تم التسليم التلقائي للطالب {attempt.user.username} في الامتحان {quiz.title}"
                )


def _auto_submit_quiz_task(attempt, quiz):
    """دالة مساعدة للتسليم التلقائي في المهام - zaki alkholy"""
    if attempt.submitted:
        return  # تم التسليم بالفعل

    answers = getattr(attempt, "answers", {}) or {}
    score = 0
    correct_count = 0
    total_questions = quiz.questions.count()
    max_score = sum(q.points for q in quiz.questions.all())

    # حساب النتيجة بناءً على الإجابات المحفوظة - zaki alkholy
    for q in quiz.questions.all():
        qid = str(q.id)
        ans_id = answers.get(qid)
        if ans_id:
            try:
                selected_answer = Answer.objects.get(id=ans_id)
                if selected_answer.is_correct:
                    score += q.points
                    correct_count += 1
            except Answer.DoesNotExist:
                pass

    # تحديث المحاولة مع معلومات التسليم التلقائي - zaki alkholy
    attempt.score = score
    attempt.passed = score >= quiz.passing_score
    attempt.submitted = True
    attempt.submitted_at = timezone.now()
    attempt.save()

    logging.info(
        f"zaki alkholy - تم التسليم التلقائي للطالب {attempt.user.username} - النتيجة: {score}/{max_score}"
    )
