from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.db import models

from ..models import (
    StudentProgress,
    StudentPoints,
    PointsHistory,
    Achievement,
    StudentAchievement,
    LessonAnalytics,
    Lesson,
    Course,
    Quiz,
)

from .user_serializer import UserSerializer
from .course_serializer import LessonSerializer

User = get_user_model()


class StudentProgressSerializer(serializers.ModelSerializer):
    """سيريالايزر لتتبع تقدم الطالب في الدروس"""
    lesson_title = serializers.CharField(source="lesson.title", read_only=True)
    course_title = serializers.CharField(source="course.title", read_only=True)
    lesson_duration = serializers.IntegerField(source="lesson.duration", read_only=True)
    
    class Meta:
        model = StudentProgress
        fields = [
            "id",
            "student",
            "lesson",
            "course",
            "lesson_title",
            "course_title",
            "lesson_duration",
            "status",
            "watch_time",
            "total_duration",
            "completion_percentage",
            "started_at",
            "completed_at",
            "last_accessed",
            "view_count",
            "last_session_start",
            "session_timeout_minutes",
            "created_at",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "lesson_title",
            "course_title",
            "lesson_duration",
        ]


class PointsHistorySerializer(serializers.ModelSerializer):
    """سيريالايزر لتاريخ النقاط"""
    lesson_title = serializers.CharField(source="lesson.title", read_only=True)
    quiz_title = serializers.CharField(source="quiz.title", read_only=True)
    course_title = serializers.CharField(source="course.title", read_only=True)
    
    class Meta:
        model = PointsHistory
        fields = [
            "id",
            "student",
            "points",
            "action",
            "reason",
            "lesson",
            "quiz",
            "course",
            "lesson_title",
            "quiz_title",
            "course_title",
            "created_at",
        ]
        read_only_fields = ["id", "created_at", "lesson_title", "quiz_title", "course_title"]


class StudentPointsSerializer(serializers.ModelSerializer):
    """سيريالايزر لنقاط الطالب"""
    student_username = serializers.CharField(source="student.username", read_only=True)
    recent_history = serializers.SerializerMethodField()
    level_progress = serializers.SerializerMethodField()
    next_level_points = serializers.SerializerMethodField()
    
    class Meta:
        model = StudentPoints
        fields = [
            "id",
            "student",
            "student_username",
            "total_points",
            "available_points",
            "spent_points",
            "current_level",
            "lessons_completed",
            "quizzes_passed",
            "courses_completed",
            "login_streak",
            "last_login_date",
            "recent_history",
            "level_progress",
            "next_level_points",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "student_username",
            "recent_history",
            "level_progress",
            "next_level_points",
            "created_at",
            "updated_at",
        ]
    
    def get_recent_history(self, obj):
        """آخر 5 عمليات نقاط"""
        recent = obj.student.points_history.all()[:5]
        return PointsHistorySerializer(recent, many=True).data
    
    def get_level_progress(self, obj):
        """تقدم الطالب في المستوى الحالي"""
        level_thresholds = {
            "bronze": 0,
            "silver": 200,
            "gold": 500,
            "platinum": 1000,
            "diamond": 2000,
        }
        
        current_threshold = level_thresholds.get(obj.current_level, 0)
        next_levels = list(level_thresholds.keys())
        
        try:
            current_index = next_levels.index(obj.current_level)
            if current_index < len(next_levels) - 1:
                next_level = next_levels[current_index + 1]
                next_threshold = level_thresholds[next_level]
                progress = ((obj.total_points - current_threshold) / 
                           (next_threshold - current_threshold)) * 100
                return min(100, max(0, progress))
        except (ValueError, ZeroDivisionError):
            pass
        
        return 100  # المستوى الأقصى
    
    def get_next_level_points(self, obj):
        """النقاط المطلوبة للمستوى التالي"""
        level_thresholds = {
            "bronze": 200,
            "silver": 500,
            "gold": 1000,
            "platinum": 2000,
            "diamond": None,  # المستوى الأقصى
        }
        
        next_threshold = level_thresholds.get(obj.current_level)
        if next_threshold and obj.total_points < next_threshold:
            return next_threshold - obj.total_points
        return 0


class AchievementSerializer(serializers.ModelSerializer):
    """سيريالايزر للإنجازات"""
    
    class Meta:
        model = Achievement
        fields = [
            "id",
            "name",
            "description",
            "icon",
            "achievement_type",
            "required_count",
            "points_reward",
            "is_active",
            "is_hidden",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class StudentAchievementSerializer(serializers.ModelSerializer):
    """سيريالايزر لإنجازات الطالب"""
    achievement = AchievementSerializer(read_only=True)
    student_username = serializers.CharField(source="student.username", read_only=True)
    related_course_title = serializers.CharField(source="related_course.title", read_only=True)
    related_lesson_title = serializers.CharField(source="related_lesson.title", read_only=True)
    related_quiz_title = serializers.CharField(source="related_quiz.title", read_only=True)
    
    class Meta:
        model = StudentAchievement
        fields = [
            "id",
            "student",
            "student_username",
            "achievement",
            "earned_at",
            "progress_when_earned",
            "related_course",
            "related_lesson",
            "related_quiz",
            "related_course_title",
            "related_lesson_title",
            "related_quiz_title",
        ]
        read_only_fields = [
            "id",
            "student_username",
            "related_course_title",
            "related_lesson_title",
            "related_quiz_title",
            "earned_at",
        ]


class LessonAnalyticsSerializer(serializers.ModelSerializer):
    """سيريالايزر لتحليلات الدروس"""
    lesson_title = serializers.CharField(source="lesson.title", read_only=True)
    
    class Meta:
        model = LessonAnalytics
        fields = [
            "id",
            "lesson",
            "lesson_title",
            "total_views",
            "unique_viewers",
            "average_watch_time",
            "completion_rate",
            "common_drop_points",
            "replay_segments",
            "average_rating",
            "difficulty_rating",
            "peak_viewing_hours",
            "last_updated",
        ]
        read_only_fields = ["id", "lesson_title", "last_updated"]


# سيريالايزر مجمع لإحصائيات الطالب
class StudentDashboardSerializer(serializers.Serializer):
    """سيريالايزر شامل للوحة تحكم الطالب"""
    points_profile = StudentPointsSerializer(read_only=True)
    recent_achievements = StudentAchievementSerializer(many=True, read_only=True)
    current_courses = serializers.SerializerMethodField()
    progress_summary = serializers.SerializerMethodField()
    
    def get_current_courses(self, obj):
        """الدورات الحالية للطالب"""
        from .course_serializer import CourseReadSerializer
        enrolled_courses = obj.enrolled_courses.filter(is_published=True)
        return CourseReadSerializer(enrolled_courses, many=True, context=self.context).data
    
    def get_progress_summary(self, obj):
        """ملخص التقدم العام"""
        progress_data = obj.lesson_progress.aggregate(
            total_lessons=models.Count('id'),
            completed_lessons=models.Count('id', filter=models.Q(status='completed')),
            total_watch_time=models.Sum('watch_time'),
        )
        
        completion_rate = 0
        if progress_data['total_lessons'] > 0:
            completion_rate = (progress_data['completed_lessons'] / progress_data['total_lessons']) * 100
        
        return {
            'total_lessons': progress_data['total_lessons'] or 0,
            'completed_lessons': progress_data['completed_lessons'] or 0,
            'completion_rate': round(completion_rate, 2),
            'total_watch_time_hours': round((progress_data['total_watch_time'] or 0) / 3600, 2),
        }


# سيريالايزر لتحديث التقدم
class UpdateProgressSerializer(serializers.Serializer):
    """سيريالايزر لتحديث تقدم الطالب"""
    lesson_id = serializers.UUIDField()
    watch_time = serializers.IntegerField(min_value=0)
    completion_percentage = serializers.IntegerField(min_value=0, max_value=100)
    status = serializers.ChoiceField(choices=StudentProgress.STATUS_CHOICES, required=False)
    
    def validate_lesson_id(self, value):
        """التحقق من وجود الدرس"""
        try:
            lesson = Lesson.objects.get(id=value)
            return value
        except Lesson.DoesNotExist:
            raise serializers.ValidationError("الدرس غير موجود")
