from django.contrib import admin
from django import forms
from django.contrib.auth.admin import UserAdmin
from .models import (
    User,
    InstructorProfile,
    Category,
    Course,
    Lesson,
    Enrollment,
    Review,
    DigitalProduct,
    Order,
    Payment,
    Certificate,
    Quiz,
    Question,
    Answer,
    UserQuizAttempt,
    Announcement,
    FAQ,
    ReviewComment,
    InstructorAvailability,
    Notification,
    MainCategory,
    # النماذج الجديدة للميزات المتقدمة - zaki alkholy
    StudentProgress,
    StudentPoints,
    PointsHistory,
    Achievement,
    StudentAchievement,
    Assignment,
    AssignmentSubmission,
    ReviewSchedule,
    LessonAnalytics,
    InstructorPayout,
    PaymentSession,
    AdminAuditLog,
)


class AdminChangePasswordForm(forms.Form):
    old_password = forms.CharField(
        widget=forms.PasswordInput, label="كلمة المرور القديمة"
    )
    new_password = forms.CharField(
        widget=forms.PasswordInput, label="كلمة المرور الجديدة"
    )
    confirm_password = forms.CharField(
        widget=forms.PasswordInput, label="تأكيد كلمة المرور الجديدة"
    )

    def clean(self):
        cleaned_data = super().clean()
        new_password = cleaned_data.get("new_password")
        confirm_password = cleaned_data.get("confirm_password")

        if new_password and confirm_password and new_password != confirm_password:
            raise forms.ValidationError("كلمتا المرور الجديدتان غير متطابقتين")
        if new_password and len(new_password) < 8:
            raise forms.ValidationError(
                "يجب أن تكون كلمة المرور الجديدة 8 أحرف على الأقل"
            )

        return True


class CustomUserAdmin(UserAdmin):
    list_display = (
        "username",
        "email",
        "phone_number",
        "wallet_number",
        "payment_method",
        "is_instructor",
        "is_staff",
        "is_student",
        "has_wallet",
        "email_verified",
    )
    list_filter = (
        "is_instructor",
        "is_staff",
        "is_superuser",
        "is_student",
        "email_verified",
    )
    search_fields = ("username", "email", "phone_number", "wallet_number")
    list_editable = ("is_instructor", "is_staff", "is_student", "has_wallet")
    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (
            "Personal info",
            {
                "fields": (
                    "first_name",
                    "last_name",
                    "email",
                    "phone_number",
                    "profile_image",
                    "bio",
                    "date_of_birth",
                )
            },
        ),
        ("Payment info", {"fields": ("wallet_number", "payment_method")}),
        (
            "Security",
            {"fields": ("email_verified", "verification_token", "last_login_ip")},
        ),
        ("Preferences", {"fields": ("language",)}),
        (
            "Permissions",
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "is_instructor",
                    "is_student",
                    "groups",
                    "user_permissions",
                    "has_wallet",
                )
            },
        ),
        ("Important dates", {"fields": ("last_login", "date_joined")}),
    )

    def get_fieldsets(self, request, obj=None):
        # إظهار كل الحقول، من غير إخفاء Payment info
        return self.fieldsets

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        # ما نشيلش الحقول، نسيب الكل يظهر
        return form

    def save_model(self, request, obj, form, change):
        # الأدمن يقدر يعدّل wallet_number بحرية
        if change and obj.pk:
            obj.wallet_number = form.cleaned_data.get("wallet_number")
        super().save_model(request, obj, form, change)


@admin.register(InstructorProfile)
class InstructorProfileAdmin(admin.ModelAdmin):
    list_display = ("user", "specialization", "is_approved")
    list_filter = ("is_approved",)
    search_fields = ("user__username", "specialization")
    raw_id_fields = ("user",)
    list_editable = ("is_approved",)
    actions = ["approve_instructors", "disapprove_instructors"]

    def approve_instructors(self, request, queryset):
        queryset.update(is_approved=True)

    approve_instructors.short_description = "Approve selected instructors"

    def disapprove_instructors(self, request, queryset):
        queryset.update(is_approved=False)

    disapprove_instructors.short_description = "Disapprove selected instructors"


@admin.register(MainCategory)
class MainCategoryAdmin(admin.ModelAdmin):
    list_display = ["name", "slug", "description"]
    search_fields = ["name", "slug"]
    prepopulated_fields = {"slug": ("name",)}


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ["name", "slug", "main_category", "description"]
    list_filter = ["main_category"]
    search_fields = ["name", "slug"]
    prepopulated_fields = {"slug": ("name",)}


class LessonInline(admin.TabularInline):
    model = Lesson
    extra = 1
    fields = ("title", "lesson_type", "order", "is_preview", "duration")


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "instructor",
        "category",
        "price",
        "level",
        "is_published",
        "is_featured",
        "likes_count",
        "get_likes_students",
    )
    list_filter = (
        "is_published",
        "instructor",
        "is_featured",
        "category",
        "level",
        "language",
        "created_at",
    )
    search_fields = ("title", "description", "short_description")
    prepopulated_fields = {"slug": ("title",)}
    raw_id_fields = ("instructor", "category")
    filter_horizontal = (
        "students",
        "likes",
    )  # إضافة likes هنا
    list_editable = ("is_published", "is_featured")
    date_hierarchy = "created_at"
    inlines = [LessonInline]
    actions = ["publish_courses", "unpublish_courses"]

    def publish_courses(self, request, queryset):
        queryset.update(is_published=True)

    publish_courses.short_description = "Publish selected courses"

    def unpublish_courses(self, request, queryset):
        queryset.update(is_published=False)

    unpublish_courses.short_description = "Unpublish selected courses"

    fieldsets = (
        (None, {"fields": ("title", "slug", "description", "short_description")}),
        (
            "Category and Instructor",
            {"fields": ("category", "instructor", "students", "likes")},
        ),  # إضافة likes هنا
        ("Pricing", {"fields": ("price", "discount_price", "currency")}),
        (
            "Educational Details",
            {"fields": ("language", "level", "prerequisites", "learning_outcomes")},
        ),
        ("Management", {"fields": ("max_students", "is_published", "is_featured")}),
        ("Media", {"fields": ("thumbnail", "promo_video")}),
    )

    def likes_count(self, obj):
        return obj.likes.count()

    likes_count.short_description = "عدد الإعجابات"

    def get_likes_students(self, obj):
        students = obj.likes.all()
        if not students:
            return "-"
        return ", ".join([u.username for u in students])

    get_likes_students.short_description = "الطلاب الذين عملوا إعجاب"


@admin.register(Lesson)
class LessonAdmin(admin.ModelAdmin):
    list_display = ("title", "course", "lesson_type", "order", "duration", "is_preview")
    list_filter = ("lesson_type", "is_preview", "course")
    search_fields = ("title", "content")
    raw_id_fields = ("course",)
    ordering = ("course", "order")
    list_editable = ("order", "is_preview")


@admin.register(Enrollment)
class EnrollmentAdmin(admin.ModelAdmin):
    list_display = ("student", "course", "progress", "completed", "enrolled_at")
    list_filter = ("completed", "enrolled_at")
    search_fields = ("student__username", "course__title")
    raw_id_fields = ("student", "course", "current_lesson")
    date_hierarchy = "enrolled_at"


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ("user", "course", "rating", "is_approved", "created_at")
    list_filter = ("rating", "is_approved", "created_at")
    search_fields = ("user__username", "course__title", "comment")
    raw_id_fields = ("user", "course")
    list_editable = ("is_approved",)
    date_hierarchy = "created_at"
    actions = ["approve_reviews", "disapprove_reviews"]

    def approve_reviews(self, request, queryset):
        queryset.update(is_approved=True)

    approve_reviews.short_description = "Approve selected reviews"

    def disapprove_reviews(self, request, queryset):
        queryset.update(is_approved=False)

    disapprove_reviews.short_description = "Disapprove selected reviews"


@admin.register(DigitalProduct)
class DigitalProductAdmin(admin.ModelAdmin):
    list_display = ("title", "seller", "price", "download_limit", "is_published")
    list_filter = ("is_published", "created_at")
    search_fields = ("title", "description")
    raw_id_fields = ("seller",)
    list_editable = ("is_published",)
    date_hierarchy = "created_at"


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ("id", "user", "amount", "status", "created_at")
    list_filter = ("status", "created_at")
    search_fields = ("user__username", "user__email", "billing_email")
    raw_id_fields = ("user", "course", "product")
    readonly_fields = ("id", "created_at", "updated_at")
    date_hierarchy = "created_at"


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ("id", "order", "amount", "payment_method", "status", "created_at")
    list_filter = ("status", "payment_method", "created_at")
    search_fields = ("order__user__username", "transaction_id")
    raw_id_fields = ("order",)
    readonly_fields = ("id", "created_at")
    date_hierarchy = "created_at"


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ("user", "course", "issued_at", "verification_code")
    list_filter = ("issued_at",)
    search_fields = ("user__username", "course__title", "verification_code")
    raw_id_fields = ("user", "course")
    readonly_fields = ("verification_code",)
    date_hierarchy = "issued_at"


class AnswerInline(admin.TabularInline):
    model = Answer
    extra = 4


# تحديث QuestionAdmin لدعم عرض الصور - زكي الخولي
@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ("text", "quiz", "question_type", "order", "points", "has_image")
    list_filter = ("question_type", "quiz")
    search_fields = ("text",)
    raw_id_fields = ("quiz",)
    inlines = [AnswerInline]
    list_editable = ("order", "points")
    readonly_fields = ("image_preview",)  # إضافة معاينة الصورة - زكي الخولي

    def has_image(self, obj):
        # عرض أيقونة إذا كان السؤال يحتوي على صورة - زكي الخولي
        return "✅" if obj.image else "❌"

    has_image.short_description = "صورة"

    def image_preview(self, obj):
        # معاينة الصورة في صفحة التحرير - زكي الخولي
        if obj.image:
            return f'<img src="{obj.image.url}" style="max-width: 200px; max-height: 200px;" />'
        return "لا توجد صورة"

    image_preview.short_description = "معاينة الصورة"
    image_preview.allow_tags = True


@admin.register(Quiz)
class QuizAdmin(admin.ModelAdmin):
    list_display = ("title", "lesson", "quiz_type", "passing_score", "time_limit")
    search_fields = ("title", "description")
    raw_id_fields = ("lesson",)


@admin.register(UserQuizAttempt)
class UserQuizAttemptAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "quiz",
        "score",
        "passed",
        "submitted",
        "submitted_at",
        "created_at",
        "completed_at",
    )
    list_filter = ("passed", "submitted", "created_at", "completed_at")
    search_fields = ("user__username", "quiz__title")
    raw_id_fields = ("user", "quiz")
    readonly_fields = (
        "score",
        "passed",
        "answers",
        "submitted",
        "submitted_at",
        "created_at",
        "completed_at",
    )
    date_hierarchy = "created_at"


@admin.register(Announcement)
class AnnouncementAdmin(admin.ModelAdmin):
    list_display = ("title", "course", "created_at")
    list_filter = ("created_at",)
    search_fields = ("title", "content", "course__title")
    raw_id_fields = ("course",)
    date_hierarchy = "created_at"


@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    list_display = ("question", "course", "order")
    list_filter = ("course",)
    search_fields = ("question", "answer", "course__title")
    raw_id_fields = ("course",)
    list_editable = ("order",)


@admin.register(ReviewComment)
class ReviewCommentAdmin(admin.ModelAdmin):
    list_display = ("id", "review", "user", "text", "parent", "created_at")
    list_filter = ("created_at", "user", "review")
    search_fields = ("text", "user__username", "review__id")
    raw_id_fields = ("user", "review", "parent")
    date_hierarchy = "created_at"


@admin.register(InstructorAvailability)
class InstructorAvailabilityAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "day",
        "from_time",
        "to_time",
        "timezone",
        "enabled",
        "created_at",
    )
    list_filter = ("day", "enabled", "timezone", "created_at")
    search_fields = ("user__username", "day", "from_time", "to_time", "timezone")
    raw_id_fields = ("user",)
    ordering = ("user", "day")


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ("user", "message", "type", "is_read", "created_at")
    list_filter = ("is_read", "type", "created_at")
    search_fields = ("user__username", "message")
    raw_id_fields = ("user",)
    date_hierarchy = "created_at"


admin.site.register(User, CustomUserAdmin)


# =============================
# Admin للميزات المتقدمة الجديدة - zaki alkholy
# =============================

@admin.register(StudentProgress)
class StudentProgressAdmin(admin.ModelAdmin):
    list_display = (
        "student",
        "lesson",
        "course",
        "status",
        "completion_percentage",
        "watch_time_minutes",
        "last_accessed",
    )
    list_filter = ("status", "course", "last_accessed")
    search_fields = ("student__username", "lesson__title", "course__title")
    raw_id_fields = ("student", "lesson", "course")
    readonly_fields = ("created_at", "watch_time_minutes")
    date_hierarchy = "last_accessed"

    def watch_time_minutes(self, obj):
        """عرض وقت المشاهدة بالدقائق"""
        return f"{obj.watch_time // 60} دقيقة" if obj.watch_time else "0 دقيقة"

    watch_time_minutes.short_description = "وقت المشاهدة"


@admin.register(StudentPoints)
class StudentPointsAdmin(admin.ModelAdmin):
    list_display = (
        "student",
        "total_points",
        "available_points",
        "spent_points",
        "current_level",
        "lessons_completed",
        "quizzes_passed",
        "login_streak",
    )
    list_filter = ("current_level", "updated_at")
    search_fields = ("student__username",)
    raw_id_fields = ("student",)
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        ("معلومات الطالب", {"fields": ("student",)}),
        ("النقاط", {"fields": ("total_points", "available_points", "spent_points")}),
        ("المستوى والإحصائيات", {
            "fields": ("current_level", "lessons_completed", "quizzes_passed", "courses_completed")
        }),
        ("سلسلة التعلم", {"fields": ("login_streak", "last_login_date")}),
        ("التواريخ", {"fields": ("created_at", "updated_at")}),
    )


@admin.register(PointsHistory)
class PointsHistoryAdmin(admin.ModelAdmin):
    list_display = ("student", "points", "action", "reason", "created_at")
    list_filter = ("action", "created_at")
    search_fields = ("student__username", "reason")
    raw_id_fields = ("student", "lesson", "quiz", "course")
    readonly_fields = ("created_at",)
    date_hierarchy = "created_at"

    def get_queryset(self, request):
        """ترتيب حسب التاريخ الأحدث"""
        return super().get_queryset(request).order_by('-created_at')


@admin.register(Achievement)
class AchievementAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "achievement_type",
        "required_count",
        "points_reward",
        "is_active",
        "is_hidden",
        "earned_count",
    )
    list_filter = ("achievement_type", "is_active", "is_hidden")
    search_fields = ("name", "description")
    list_editable = ("is_active", "is_hidden")

    fieldsets = (
        ("معلومات الإنجاز", {"fields": ("name", "description", "icon")}),
        ("شروط الحصول", {"fields": ("achievement_type", "required_count")}),
        ("المكافآت", {"fields": ("points_reward",)}),
        ("الحالة", {"fields": ("is_active", "is_hidden")}),
    )

    def earned_count(self, obj):
        """عدد الطلاب الذين حصلوا على هذا الإنجاز"""
        return obj.student_achievements.count()

    earned_count.short_description = "عدد الحاصلين عليه"


@admin.register(StudentAchievement)
class StudentAchievementAdmin(admin.ModelAdmin):
    list_display = (
        "student",
        "achievement",
        "earned_at",
        "progress_when_earned",
        "related_course",
    )
    list_filter = ("achievement", "earned_at")
    search_fields = ("student__username", "achievement__name")
    raw_id_fields = ("student", "achievement", "related_course", "related_lesson", "related_quiz")
    readonly_fields = ("earned_at",)
    date_hierarchy = "earned_at"


@admin.register(Assignment)
class AssignmentAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "lesson",
        "assignment_type",
        "max_score",
        "due_date",
        "is_published",
        "submissions_count",
    )
    list_filter = ("assignment_type", "is_published", "due_date")
    search_fields = ("title", "description", "lesson__title")
    raw_id_fields = ("lesson",)
    list_editable = ("is_published",)
    date_hierarchy = "due_date"

    fieldsets = (
        ("معلومات الواجب", {"fields": ("lesson", "title", "description", "instructions")}),
        ("نوع الواجب", {"fields": ("assignment_type",)}),
        ("التقييم", {"fields": ("max_score", "passing_score")}),
        ("المواعيد", {"fields": ("due_date", "late_submission_allowed", "late_penalty_percentage")}),
        ("الملفات", {"fields": ("allowed_file_types", "max_file_size_mb")}),
        ("الحالة", {"fields": ("is_published", "auto_grade")}),
    )

    def submissions_count(self, obj):
        """عدد التسليمات"""
        return obj.submissions.count()

    submissions_count.short_description = "عدد التسليمات"


@admin.register(AssignmentSubmission)
class AssignmentSubmissionAdmin(admin.ModelAdmin):
    list_display = (
        "student",
        "assignment",
        "status",
        "score",
        "final_score",
        "submitted_at",
        "is_late",
        "attempt_number",
    )
    list_filter = ("status", "is_late", "submitted_at")
    search_fields = ("student__username", "assignment__title")
    raw_id_fields = ("assignment", "student")
    readonly_fields = ("submitted_at", "graded_at", "created_at", "updated_at", "final_score")
    date_hierarchy = "submitted_at"

    fieldsets = (
        ("معلومات التسليم", {"fields": ("assignment", "student", "attempt_number")}),
        ("المحتوى", {"fields": ("text_content", "file_upload", "submission_notes")}),
        ("التقييم", {"fields": ("score", "feedback", "status")}),
        ("معلومات إضافية", {"fields": ("is_late", "final_score")}),
        ("التواريخ", {"fields": ("submitted_at", "graded_at", "created_at", "updated_at")}),
    )

    def final_score(self, obj):
        """الدرجة النهائية مع خصم التأخير"""
        return obj.calculate_final_score()

    final_score.short_description = "الدرجة النهائية"


@admin.register(ReviewSchedule)
class ReviewScheduleAdmin(admin.ModelAdmin):
    list_display = (
        "student",
        "content_title",
        "content_type",
        "next_review_date",
        "review_count",
        "success_count",
        "difficulty_level",
        "is_active",
        "is_mastered",
    )
    list_filter = ("content_type", "is_active", "is_mastered", "difficulty_level", "next_review_date")
    search_fields = ("student__username", "content_title")
    raw_id_fields = ("student", "lesson", "quiz")
    readonly_fields = ("created_at", "updated_at", "success_rate")
    date_hierarchy = "next_review_date"

    fieldsets = (
        ("معلومات المراجعة", {"fields": ("student", "lesson", "quiz")}),
        ("المحتوى", {"fields": ("content_type", "content_title", "content_summary")}),
        ("الجدولة", {"fields": ("initial_learned_date", "next_review_date", "review_interval_days")}),
        ("الأداء", {"fields": ("review_count", "success_count", "success_rate", "difficulty_level")}),
        ("الحالة", {"fields": ("is_active", "is_mastered")}),
        ("التواريخ", {"fields": ("created_at", "updated_at")}),
    )

    def success_rate(self, obj):
        """معدل النجاح في المراجعة"""
        if obj.review_count > 0:
            return f"{(obj.success_count / obj.review_count) * 100:.1f}%"
        return "0%"

    success_rate.short_description = "معدل النجاح"


@admin.register(LessonAnalytics)
class LessonAnalyticsAdmin(admin.ModelAdmin):
    list_display = (
        "lesson",
        "total_views",
        "unique_viewers",
        "completion_rate",
        "average_rating",
        "last_updated",
    )
    list_filter = ("last_updated",)
    search_fields = ("lesson__title",)
    raw_id_fields = ("lesson",)
    readonly_fields = ("last_updated",)

    fieldsets = (
        ("الدرس", {"fields": ("lesson",)}),
        ("إحصائيات المشاهدة", {"fields": ("total_views", "unique_viewers", "average_watch_time", "completion_rate")}),
        ("التفاعل", {"fields": ("common_drop_points", "replay_segments")}),
        ("التقييمات", {"fields": ("average_rating", "difficulty_rating")}),
        ("التوقيت", {"fields": ("peak_viewing_hours", "last_updated")}),
    )


@admin.register(InstructorPayout)
class InstructorPayoutAdmin(admin.ModelAdmin):
    list_display = (
        "instructor",
        "amount_paid",
        "status",
        "created_at",
        "completed_at",
        "processed_by",
    )
    list_filter = ("status", "created_at", "completed_at")
    search_fields = ("instructor__username", "instructor__email")
    raw_id_fields = ("instructor", "processed_by")
    filter_horizontal = ("orders",)
    readonly_fields = ("created_at",)
    date_hierarchy = "created_at"

    fieldsets = (
        ("معلومات المعلم", {"fields": ("instructor",)}),
        ("تفاصيل التحويل", {"fields": ("orders", "total_amount", "platform_fee", "amount_paid")}),
        ("الحالة والمعالجة", {"fields": ("status", "receipt", "notes", "processed_by")}),
        ("التوقيت", {"fields": ("created_at", "completed_at")}),
    )

    def completion_rate_percent(self, obj):
        """معدل الإكمال كنسبة مئوية"""
        return f"{obj.completion_rate:.1f}%" if obj.completion_rate else "0%"

    completion_rate_percent.short_description = "معدل الإكمال"


@admin.register(PaymentSession)
class PaymentSessionAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "user",
        "course",
        "amount",
        "payment_method",
        "created_at",
        "expires_at",
        "is_expired_status",
    )
    list_filter = ("payment_method", "created_at", "expires_at")
    search_fields = ("user__username", "course__title", "paymob_order_id")
    readonly_fields = ("created_at", "is_expired_status")
    raw_id_fields = ("user", "course")

    fieldsets = (
        ("معلومات الجلسة", {"fields": ("user", "course", "paymob_order_id")}),
        ("تفاصيل الدفع", {"fields": ("payment_method", "phone_number", "amount")}),
        ("تفاصيل السعر", {"fields": ("base_price", "paymob_fee", "platform_fee")}),
        ("معلومات الفوترة", {"fields": ("billing_email", "billing_name")}),
        ("التوقيت", {"fields": ("created_at", "expires_at", "is_expired_status")}),
    )

    def is_expired_status(self, obj):
        """حالة انتهاء الصلاحية"""
        if obj.is_expired():
            return "منتهية الصلاحية"
        return "نشطة"

    is_expired_status.short_description = "حالة الصلاحية"

    actions = ["delete_expired_sessions"]

    def delete_expired_sessions(self, request, queryset):
        """حذف الجلسات منتهية الصلاحية"""
        expired_count = 0
        for session in queryset:
            if session.is_expired():
                session.delete()
                expired_count += 1

        self.message_user(request, f"تم حذف {expired_count} جلسة منتهية الصلاحية")

    delete_expired_sessions.short_description = "حذف الجلسات منتهية الصلاحية"


@admin.register(AdminAuditLog)
class AdminAuditLogAdmin(admin.ModelAdmin):
    list_display = (
        "timestamp",
        "admin_username",
        "action_type",
        "severity",
        "target_model",
        "target_object_repr",
        "ip_address",
    )
    list_filter = (
        "action_type",
        "severity",
        "target_model",
        "timestamp",
        "admin_user",
    )
    search_fields = (
        "admin_username",
        "admin_email",
        "action_description",
        "target_object_repr",
        "ip_address",
    )
    readonly_fields = (
        "timestamp",
        "admin_user",
        "admin_username",
        "admin_email",
        "action_type",
        "action_description",
        "severity",
        "target_model",
        "target_object_id",
        "target_object_repr",
        "old_values",
        "new_values",
        "additional_data",
        "ip_address",
        "user_agent",
        "session_key",
    )

    fieldsets = (
        ("معلومات العمل", {
            "fields": (
                "timestamp",
                "action_type",
                "action_description",
                "severity",
            )
        }),
        ("معلومات المدير", {
            "fields": (
                "admin_user",
                "admin_username",
                "admin_email",
            )
        }),
        ("الكائن المتأثر", {
            "fields": (
                "target_model",
                "target_object_id",
                "target_object_repr",
            )
        }),
        ("البيانات", {
            "fields": (
                "old_values",
                "new_values",
                "additional_data",
            ),
            "classes": ("collapse",),
        }),
        ("معلومات تقنية", {
            "fields": (
                "ip_address",
                "user_agent",
                "session_key",
            ),
            "classes": ("collapse",),
        }),
    )

    date_hierarchy = "timestamp"
    ordering = ["-timestamp"]

    def has_add_permission(self, request):
        """منع إضافة سجلات يدوياً"""
        return False

    def has_change_permission(self, request, obj=None):
        """منع تعديل السجلات"""
        return False

    def has_delete_permission(self, request, obj=None):
        """السماح بالحذف للمديرين فقط"""
        return request.user.is_superuser

    actions = ["export_audit_logs", "mark_as_reviewed"]

    def export_audit_logs(self, request, queryset):
        """تصدير سجلات المراجعة"""
        # هنا يمكن إضافة منطق التصدير
        count = queryset.count()
        self.message_user(request, f"تم تصدير {count} سجل مراجعة")

    export_audit_logs.short_description = "تصدير السجلات المحددة"

    def mark_as_reviewed(self, request, queryset):
        """وضع علامة مراجعة"""
        # يمكن إضافة حقل "reviewed" للموديل لاحقاً
        count = queryset.count()
        self.message_user(request, f"تم وضع علامة مراجعة على {count} سجل")

    mark_as_reviewed.short_description = "وضع علامة مراجعة"
