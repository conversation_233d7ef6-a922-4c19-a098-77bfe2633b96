# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.parsers import MultiPartParser, FormParser

# Django imports
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

# Models
from ..models import (
    Assignment,
    AssignmentSubmission,
    Lesson,
    Course,
    StudentPoints,
)

# Serializers
from ..serializers_file.assignment_serializer import (
    AssignmentSerializer,
    AssignmentSubmissionSerializer,
    AssignmentSubmissionCreateSerializer,
    AssignmentGradingSerializer,
    StudentAssignmentListSerializer,
    InstructorAssignmentStatsSerializer,
)

User = get_user_model()


class AssignmentViewSet(viewsets.ModelViewSet):
    """ViewSet لإدارة الواجبات التفاعلية"""
    serializer_class = AssignmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام UUID للحماية القصوى - zaki alkholy
    
    def get_queryset(self):
        """الحصول على الواجبات حسب نوع المستخدم"""
        if self.request.user.is_instructor:
            # المعلم يرى واجباته فقط
            return Assignment.objects.filter(
                lesson__course__instructor=self.request.user
            ).select_related('lesson__course')
        else:
            # الطالب يرى الواجبات للدورات المسجل فيها
            return Assignment.objects.filter(
                lesson__course__students=self.request.user,
                is_published=True
            ).select_related('lesson__course')
    
    def perform_create(self, serializer):
        """إنشاء واجب جديد (للمعلمين فقط)"""
        if not self.request.user.is_instructor:
            raise permissions.PermissionDenied("هذه الخدمة متاحة للمعلمين فقط")
        
        lesson = serializer.validated_data['lesson']
        if lesson.course.instructor != self.request.user:
            raise permissions.PermissionDenied("ليس لديك صلاحية لإنشاء واجب في هذا الدرس")
        
        serializer.save()
    
    @action(detail=True, methods=['get'])
    def submissions(self, request, id=None):
        """الحصول على تسليمات الواجب (للمعلمين)"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        assignment = self.get_object()
        if assignment.lesson.course.instructor != request.user:
            return Response(
                {"error": "ليس لديك صلاحية للوصول لهذا الواجب"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        submissions = AssignmentSubmission.objects.filter(
            assignment=assignment
        ).select_related('student').order_by('-submitted_at')
        
        # فلترة حسب الحالة
        status_filter = request.query_params.get('status')
        if status_filter:
            submissions = submissions.filter(status=status_filter)
        
        serializer = AssignmentSubmissionSerializer(
            submissions, 
            many=True, 
            context={'request': request}
        )
        
        # إحصائيات سريعة
        stats = {
            "total_submissions": submissions.count(),
            "submitted": submissions.filter(status='submitted').count(),
            "graded": submissions.filter(status='graded').count(),
            "draft": submissions.filter(status='draft').count(),
            "average_score": submissions.filter(
                status='graded', 
                score__isnull=False
            ).aggregate(avg=Avg('score'))['avg'] or 0,
        }
        
        return Response({
            "submissions": serializer.data,
            "stats": stats,
        })
    
    @action(detail=True, methods=['post'])
    def grade_submission(self, request, pk=None):
        """تقييم تسليم واجب (للمعلمين)"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        assignment = self.get_object()
        if assignment.lesson.course.instructor != request.user:
            return Response(
                {"error": "ليس لديك صلاحية للوصول لهذا الواجب"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        submission_id = request.data.get('submission_id')
        if not submission_id:
            return Response(
                {"error": "معرف التسليم مطلوب"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            submission = AssignmentSubmission.objects.get(
                id=submission_id,
                assignment=assignment
            )
        except AssignmentSubmission.DoesNotExist:
            return Response(
                {"error": "التسليم غير موجود"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = AssignmentGradingSerializer(
            submission,
            data=request.data,
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "تم تقييم الواجب بنجاح",
                "submission": serializer.data
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def my_assignments(self, request):
        """واجبات الطالب الحالي"""
        if request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للطلاب فقط"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # الحصول على جميع الواجبات للطالب
        assignments = Assignment.objects.filter(
            lesson__course__students=request.user,
            is_published=True
        ).select_related('lesson__course')
        
        # الحصول على تسليمات الطالب
        submissions = AssignmentSubmission.objects.filter(
            student=request.user
        ).select_related('assignment')
        
        # ربط الواجبات بالتسليمات
        assignment_data = []
        for assignment in assignments:
            # البحث عن آخر تسليم للطالب
            student_submission = submissions.filter(assignment=assignment).first()
            
            assignment_info = AssignmentSerializer(assignment, context={'request': request}).data
            assignment_info['student_submission'] = None
            
            if student_submission:
                assignment_info['student_submission'] = AssignmentSubmissionSerializer(
                    student_submission,
                    context={'request': request}
                ).data
            
            assignment_data.append(assignment_info)
        
        # تصنيف الواجبات
        categorized = {
            "pending": [],      # لم يتم التسليم
            "submitted": [],    # تم التسليم وفي انتظار التقييم
            "graded": [],       # تم التقييم
            "overdue": [],      # متأخر
        }
        
        for assignment in assignment_data:
            submission = assignment['student_submission']
            
            if not submission:
                if assignment['is_overdue']:
                    categorized["overdue"].append(assignment)
                else:
                    categorized["pending"].append(assignment)
            elif submission['status'] == 'submitted':
                categorized["submitted"].append(assignment)
            elif submission['status'] == 'graded':
                categorized["graded"].append(assignment)
        
        return Response({
            "all_assignments": assignment_data,
            "categorized": categorized,
            "stats": {
                "total": len(assignment_data),
                "pending": len(categorized["pending"]),
                "submitted": len(categorized["submitted"]),
                "graded": len(categorized["graded"]),
                "overdue": len(categorized["overdue"]),
            }
        })


class AssignmentSubmissionViewSet(viewsets.ModelViewSet):
    """ViewSet لإدارة تسليم الواجبات"""
    serializer_class = AssignmentSubmissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    lookup_field = 'id'  # استخدام UUID للحماية القصوى - zaki alkholy
    
    def get_queryset(self):
        """الحصول على تسليمات الطالب فقط"""
        return AssignmentSubmission.objects.filter(
            student=self.request.user
        ).select_related('assignment__lesson__course')
    
    def get_serializer_class(self):
        """اختيار الـ serializer المناسب"""
        if self.action == 'create':
            return AssignmentSubmissionCreateSerializer
        return AssignmentSubmissionSerializer
    
    def perform_create(self, serializer):
        """إنشاء تسليم جديد"""
        serializer.save()
    
    @action(detail=True, methods=['post'])
    def submit(self, request, pk=None):
        """تسليم الواجب نهائياً"""
        submission = self.get_object()
        
        if submission.status != 'draft':
            return Response(
                {"error": "تم تسليم الواجب مسبقاً"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # التحقق من موعد التسليم
        if submission.assignment.is_overdue() and not submission.assignment.late_submission_allowed:
            return Response(
                {"error": "انتهى موعد تسليم الواجب"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # تحديث حالة التسليم
        submission.status = 'submitted'
        submission.submitted_at = timezone.now()
        submission.is_late = submission.assignment.is_overdue()
        submission.save()
        
        return Response({
            "message": "تم تسليم الواجب بنجاح",
            "submission": AssignmentSubmissionSerializer(
                submission,
                context={'request': request}
            ).data
        })
    
    @action(detail=True, methods=['post'])
    def save_draft(self, request, pk=None):
        """حفظ الواجب كمسودة"""
        submission = self.get_object()
        
        if submission.status != 'draft':
            return Response(
                {"error": "لا يمكن تعديل واجب تم تسليمه"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # تحديث المحتوى
        text_content = request.data.get('text_content', '')
        submission_notes = request.data.get('submission_notes', '')
        
        submission.text_content = text_content
        submission.submission_notes = submission_notes
        
        # تحديث الملف إذا تم رفع ملف جديد
        if 'file_upload' in request.FILES:
            submission.file_upload = request.FILES['file_upload']
        
        submission.save()
        
        return Response({
            "message": "تم حفظ المسودة بنجاح",
            "submission": AssignmentSubmissionSerializer(
                submission,
                context={'request': request}
            ).data
        })


class InstructorAssignmentAnalyticsAPIView(APIView):
    """تحليلات الواجبات للمعلم"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على تحليلات الواجبات"""
        if not request.user.is_instructor:
            return Response(
                {"error": "هذه الخدمة متاحة للمعلمين فقط"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        instructor = request.user
        
        # جميع واجبات المعلم
        assignments = Assignment.objects.filter(
            lesson__course__instructor=instructor
        )
        
        # إحصائيات عامة
        total_assignments = assignments.count()
        published_assignments = assignments.filter(is_published=True).count()
        
        # إحصائيات التسليمات
        all_submissions = AssignmentSubmission.objects.filter(
            assignment__lesson__course__instructor=instructor
        )
        
        total_submissions = all_submissions.count()
        graded_submissions = all_submissions.filter(status='graded').count()
        pending_submissions = all_submissions.filter(status='submitted').count()
        
        # متوسط الدرجات
        average_score = all_submissions.filter(
            status='graded',
            score__isnull=False
        ).aggregate(avg=Avg('score'))['avg'] or 0
        
        # معدل النجاح
        passing_submissions = all_submissions.filter(
            status='graded',
            score__gte=F('assignment__passing_score')
        ).count()
        
        pass_rate = (passing_submissions / graded_submissions * 100) if graded_submissions > 0 else 0
        
        # الواجبات الأكثر صعوبة (أقل معدل نجاح)
        difficult_assignments = assignments.annotate(
            submission_count=Count('submissions', filter=Q(submissions__status='graded')),
            avg_score=Avg('submissions__score', filter=Q(submissions__status='graded')),
            pass_count=Count('submissions', filter=Q(
                submissions__status='graded',
                submissions__score__gte=F('passing_score')
            ))
        ).filter(submission_count__gt=0).order_by('avg_score')[:5]
        
        difficult_assignments_data = [
            {
                "assignment_id": str(assignment.id),
                "title": assignment.title,
                "course_title": assignment.lesson.course.title,
                "average_score": round(assignment.avg_score or 0, 2),
                "pass_rate": round((assignment.pass_count / assignment.submission_count * 100), 2) if assignment.submission_count > 0 else 0,
                "submission_count": assignment.submission_count,
            }
            for assignment in difficult_assignments
        ]
        
        # التسليمات المتأخرة
        late_submissions = all_submissions.filter(is_late=True).count()
        late_rate = (late_submissions / total_submissions * 100) if total_submissions > 0 else 0
        
        return Response({
            "overview": {
                "total_assignments": total_assignments,
                "published_assignments": published_assignments,
                "total_submissions": total_submissions,
                "graded_submissions": graded_submissions,
                "pending_submissions": pending_submissions,
                "average_score": round(average_score, 2),
                "pass_rate": round(pass_rate, 2),
                "late_submissions": late_submissions,
                "late_rate": round(late_rate, 2),
            },
            "difficult_assignments": difficult_assignments_data,
        })
