from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.conf import settings
from django.views.decorators.http import require_http_methods
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model, authenticate
import json
import time

# Hossam third code of hls
from .video_converter import VideoConverter

# Custom throttles for quiz system
from .throttles import QuizSaveAnswerThrottle, QuizGeneralThrottle

from .models import (
    Course,
    DigitalProduct,
    Order,
    Payment,
    Lesson,
    Enrollment,
    Quiz,
    Question,
    Answer,
    UserQuizAttempt,
    Certificate,
    Announcement,
    FAQ,
    Category,
    InstructorProfile,
    Review,
    InstructorAvailability,
    Notification,
    ReviewComment,
    MainCategory,
)


# User-related
from .serializers_file.user_serializer import (
    UserSerializer,
)


# Course & Content-related
from .serializers_file.course_serializer import (
    CourseReadSerializer,
    CourseWriteSerializer,
    ReviewSerializer,
    LessonSerializer,
    CategorySerializer,
    MainCategorySerializer,
    FAQSerializer,
    AnnouncementSerializer,
    CertificateSerializer,
)

# Instructor-related
from .serializers_file.instructor_serializer import (
    InstructorWithCoursesSerializer,
    InstructorProfileSerializer,
    InstructorAvailabilitySerializer,
)

# Quiz-related
from .serializers_file.quize_serializer import (
    QuizSerializer,
    QuestionSerializer,
    AnswerSerializer,
)

# Orders & Payments
from .serializers import (
    OrderSerializer,
    PaymentSerializer,
    NotificationSerializer,
    DigitalProductSerializer,
)

from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
import uuid
import hmac
import hashlib
import time
from datetime import datetime, timedelta
import os
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from PIL import Image, ImageDraw, ImageFont
import io
import boto3
from botocore.exceptions import ClientError
import ffmpeg
import tempfile
from rest_framework.views import APIView
import jwt
import logging
from rest_framework_simplejwt.tokens import RefreshToken
import re
from django.db.models import Q, Sum
from .cloudinary_service import CloudinaryVideoService
from rest_framework import serializers
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from main.utils.notifications import notify
from main.constants.notification_messages import (
    COURSE_CREATED,
    COURSE_DELETED_STUDENT,
    COURSE_DELETED_INSTRUCTOR,
    COURSE_UPDATED,
    VIDEO_UPLOADED,
    VIDEO_DELETED,
    ANNOUNCEMENT_NEW,
    PAYMENT_RECEIVED,
    PAYMENT_FOR_COURSE,
    CERTIFICATE_ISSUED,
    QUIZ_CERTIFICATE_CONGRATS,
    REVIEW_COMMENT_REPLY,
    REVIEW_COMMENT_NEW,
    REVIEW_COMMENT_EDIT,
    REVIEW_REPLY_EDIT,
    REVIEW_COMMENT_DELETE,
    REVIEW_EDIT,
    REVIEW_DELETE,
    REVIEW_APPROVED,
    ACCOUNT_UPDATED,
    PASSWORD_CHANGED,
)
from main.constants.notification_types import (
    COURSE,
    REVIEW,
    PAYMENT,
    CERTIFICATE,
    SYSTEM,
)
from main.tasks import send_bulk_notification
import traceback
from .permissions import IsInstructor
from google.oauth2 import id_token
from google.auth.transport import requests
import secrets
from django.core.mail import send_mail


logger = logging.getLogger(__name__)
User = get_user_model()


class GetUsersView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            users = User.objects.all()
            user_data = [
                {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                for user in users
            ]
            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                }
            )
        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID للمستخدمين - zaki alkholy

    def get_queryset(self):
        if self.request.user.is_staff:
            return User.objects.all()
        return User.objects.filter(id=self.request.user.id)

    def perform_update(self, serializer):
        # ✅ جلب نسخة المستخدم قبل الحفظ للمقارنة
        original = User.objects.get(pk=self.get_object().pk)

        # 📝 حفظ البيانات الجديدة
        user = serializer.save()

        # ✅ تحقق من حالة المحفظة
        if user.wallet_number:
            # ❌ محاولة تعديل wallet_number بعد ما اتسجل قبل كده
            if original.wallet_number and original.wallet_number != user.wallet_number:
                raise serializers.ValidationError(
                    "Wallet number cannot be changed once set."
                )

            # ✅ أول مرة يتم تعيين wallet_number
            if not original.wallet_number and not original.has_wallet:
                user.has_wallet = True
                user.save(update_fields=["has_wallet"])

        # إشعارات أمان عند تعديل الحقول الحساسة
        changed_fields = []
        if hasattr(self, "request") and self.request.method in ["PUT", "PATCH"]:
            for field, label in [
                ("email", "البريد الإلكتروني"),
                ("phone_number", "رقم الجوال"),
                ("username", "اسم المستخدم"),
            ]:
                if (
                    field in self.request.data
                    and getattr(original, field) != self.request.data[field]
                ):
                    changed_fields.append(label)

        for field_label in changed_fields:
            notify(user, ACCOUNT_UPDATED(field_label), type=SYSTEM)

        return user

    @action(detail=False, methods=["get"], url_path="list-all")
    def list_all(self, request):
        self.throttle_scope = "search_users"
        self.check_throttles(request)
        try:
            users = User.objects.all()
            user_data = [
                {
                    "id": str(user.id),
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                for user in users
            ]
            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                }
            )
        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def all_users(self, request):
        try:
            users = User.objects.all()
            user_data = [
                {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                for user in users
            ]
            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                }
            )
        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["post"],
        url_path="change-password",
        permission_classes=[IsAuthenticated],
    )
    def change_password(self, request):
        user = request.user
        current_password = request.data.get("current_password")
        new_password = request.data.get("new_password")
        if not current_password or not new_password:
            return Response(
                {"error": "يجب إدخال كلمة المرور الحالية والجديدة"}, status=400
            )
        if not user.check_password(current_password):
            return Response({"error": "كلمة المرور الحالية غير صحيحة"}, status=400)
        user.set_password(new_password)
        user.save()
        notify(user, PASSWORD_CHANGED, type=SYSTEM)
        return Response({"message": "تم تغيير كلمة المرور بنجاح"})

    @action(detail=False, methods=["get"], url_path="search-instructors")
    def search_instructors(self, request):
        self.check_throttles(request)
        query = request.query_params.get("q", "").strip()
        if not query:
            return Response({"users": []})

        users = User.objects.filter(is_instructor=True, username__icontains=query)
        page = self.paginate_queryset(users)
        if page is not None:
            data = UserSerializer(page, many=True, context={"request": request}).data
            return self.get_paginated_response(data)

        data = UserSerializer(users, many=True, context={"request": request}).data
        return Response({"users": data})


class OrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID للطلبات - zaki alkholy

    def get_queryset(self):
        if self.request.user.is_staff:
            return Order.objects.all()
        return Order.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        order = serializer.save(user=self.request.user)
        # إرسال إشعار بعد الشراء
        if order.course:
            notify(
                self.request.user,
                f"تم تأكيد طلبك بنجاح للدورة: {order.course.title}",
                type=SYSTEM,
            )
        elif order.product:
            notify(
                self.request.user,
                f"تم تأكيد طلبك للمنتج الرقمي: {order.product.title}",
                type=SYSTEM,
            )


class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID للمدفوعات - zaki alkholy

    def get_queryset(self):
        if self.request.user.is_staff:
            return Payment.objects.all()
        return Payment.objects.filter(order__user=self.request.user)

    def perform_create(self, serializer):
        payment = serializer.save()
        # إشعار للمستخدم الذي دفع
        notify(payment.order.user, PAYMENT_RECEIVED(payment.order.id), type=PAYMENT)
        # إشعار للمدرب إذا كان الطلب مرتبط بكورس وله مدرب
        if payment.order.course and hasattr(payment.order.course, "instructor"):
            notify(
                payment.order.course.instructor,
                PAYMENT_FOR_COURSE(payment.order.course.title),
                type=PAYMENT,
            )


class UserListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # جلب جميع المستخدمين
            users = User.objects.all()

            # التحقق من وجود مستخدمين
            if not users.exists():
                return Response(
                    {"message": "لا يوجد مستخدمين في النظام", "users": []},
                    status=status.HTTP_200_OK,
                )

            # تحويل البيانات إلى القائمة المطلوبة
            user_data = []
            for user in users:
                user_info = {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                user_data.append(user_info)

            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ReviewViewSet(viewsets.ModelViewSet):
    queryset = Review.objects.all()  # يشمل كل التعليقات
    serializer_class = ReviewSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    lookup_field = 'id'  # استخدام ID للتقييمات - zaki alkholy

    def get_permissions(self):
        """
        تخصيص الصلاحيات حسب العملية
        """
        if self.action in ['update', 'partial_update']:
            # المعلم يقدر يعدل التقييمات لكورساته فقط
            return [IsAuthenticated()]
        return super().get_permissions()

    def get_queryset(self):
        queryset = Review.objects.all()

        # فلترة بناءً على الكورس إذا تم تمرير course parameter
        course_id = self.request.query_params.get('course', None)
        if course_id is not None:
            queryset = queryset.filter(course__slug=course_id)

        # إذا كان الطلب list فقط أظهر المعتمدة، غير ذلك أظهر الكل
        if self.action == "list":
            queryset = queryset.filter(is_approved=True)

        return queryset

    def perform_create(self, serializer):
        serializer.save(user=self.request.user, is_approved=False)

    def perform_update(self, serializer):
        """
        التحقق من صلاحية المعلم لتعديل التقييم
        """
        review = self.get_object()
        user = self.request.user

        # التحقق من أن المستخدم هو معلم الكورس أو admin
        if user.is_staff or (user.is_instructor and review.course.instructor == user):
            serializer.save()
        else:
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("غير مصرح لك بتعديل هذا التقييم")

    @action(detail=True, methods=["get"], url_path="comments")
    def comments(self, request, id=None):
        review = self.get_object()
        comments = review.comments.filter(parent__isnull=True).order_by("created_at")
        # استخدام ReviewCommentSerializer بدلاً من ReviewSerializer - zaki alkholy
        from .serializers_file.course_serializer import ReviewCommentSerializer
        serializer = ReviewCommentSerializer(comments, many=True, context={"request": request})
        return Response(serializer.data)

    @action(detail=True, methods=["post"], url_path="add_comment")
    def add_comment(self, request, id=None):
        review = self.get_object()
        course = review.course

        # التحقق من الصلاحيات: المعلم أو صاحب التقييم فقط
        if not (request.user == review.user or
               (request.user.is_instructor and course.instructor == request.user)):
            return Response({"error": "غير مصرح لك بالتعليق على هذا التقييم"}, status=403)

        text = request.data.get("text")
        parent_id = request.data.get("parent")
        parent = None

        if parent_id:
            # التحقق من أن parent_id هو UUID صالح
            try:
                import uuid
                uuid.UUID(str(parent_id))  # التحقق من صحة UUID
                parent = ReviewComment.objects.get(id=parent_id, review=review)
            except (ValueError, ReviewComment.DoesNotExist):
                return Response({"error": "Parent comment not found or invalid ID"}, status=404)
        comment = ReviewComment.objects.create(
            review=review, user=request.user, text=text, parent=parent
        )
        if parent:
            # إشعار لصاحب التعليق الأصلي
            notify(parent.user, REVIEW_COMMENT_REPLY(review.course.title), type=REVIEW)
        else:
            # إشعار لصاحب المراجعة
            notify(review.user, REVIEW_COMMENT_NEW(review.course.title), type=REVIEW)
        # استخدام ReviewCommentSerializer بدلاً من ReviewSerializer - zaki alkholy
        from .serializers_file.course_serializer import ReviewCommentSerializer
        serializer = ReviewCommentSerializer(comment, context={"request": request})
        return Response(serializer.data, status=201)

    @action(
        detail=True,
        methods=["patch"],
        url_path="edit_comment",
        permission_classes=[IsAuthenticated],
    )
    def edit_comment(self, request, id=None):
        try:
            comment = ReviewComment.objects.get(id=pk, user=request.user)
            comment.text = request.data.get("text", comment.text)
            comment.save()
            # إرسال إشعار لصاحب المراجعة إذا كان التعليق بدون رد
            if comment.parent is None:
                notify(
                    comment.review.user,
                    REVIEW_COMMENT_EDIT(
                        request.user.username, comment.review.course.title
                    ),
                    type=REVIEW,
                )
            # أو لصاحب التعليق الأصلي إذا كان هذا رد
            else:
                notify(
                    comment.parent.user,
                    REVIEW_REPLY_EDIT(
                        request.user.username, comment.review.course.title
                    ),
                    type=REVIEW,
                )
            return Response({"message": "تم تعديل التعليق بنجاح"}, status=200)
        except ReviewComment.DoesNotExist:
            return Response({"error": "التعليق غير موجود أو غير مسموح"}, status=404)

    @action(
        detail=True,
        methods=["delete"],
        url_path="delete_comment/(?P<comment_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def delete_comment(self, request, id=None, comment_id=None):
        try:
            review = self.get_object()
            comment = ReviewComment.objects.get(id=comment_id, review=review)

            # التحقق من الصلاحيات: المعلم أو صاحب التعليق
            if not (request.user == comment.user or
                   (request.user.is_instructor and review.course.instructor == request.user)):
                return Response({"error": "غير مصرح لك بحذف هذا التعليق"}, status=403)

            comment = ReviewComment.objects.get(id=comment_id, review=review)
            recipient = comment.parent.user if comment.parent else comment.review.user
            course_title = comment.review.course.title
            comment.delete()
            notify(
                recipient,
                REVIEW_COMMENT_DELETE(request.user.username, course_title),
                type=REVIEW,
            )
            return Response({"message": "تم حذف التعليق بنجاح"}, status=204)
        except ReviewComment.DoesNotExist:
            return Response({"error": "التعليق غير موجود أو غير مسموح"}, status=404)

    @action(
        detail=True,
        methods=["patch"],
        url_path="edit_review",
        permission_classes=[IsAuthenticated],
    )
    def edit_review(self, request, pk=None):
        review = self.get_object()
        if review.user != request.user:
            return Response({"error": "غير مصرح لك بتعديل هذه المراجعة"}, status=403)
        review.text = request.data.get("text", review.text)
        review.rating = request.data.get("rating", review.rating)
        review.save()
        notify(
            review.course.instructor,
            REVIEW_EDIT(request.user.username, review.course.title),
            type=REVIEW,
        )
        return Response({"message": "تم تعديل المراجعة بنجاح"}, status=200)

    @action(
        detail=True,
        methods=["delete"],
        url_path="delete_review",
        permission_classes=[IsAuthenticated],
    )
    def delete_review(self, request, pk=None):
        review = self.get_object()
        if review.user != request.user:
            return Response({"error": "غير مصرح لك بالحذف"}, status=403)
        course_title = review.course.title
        instructor = review.course.instructor
        review.delete()
        notify(
            instructor, REVIEW_DELETE(request.user.username, course_title), type=REVIEW
        )
        return Response({"message": "تم حذف المراجعة بنجاح"}, status=204)

    @action(
        detail=True,
        methods=["patch"],
        url_path="approve_review",
        permission_classes=[IsAuthenticated],
    )
    def approve_review(self, request, pk=None):
        if not request.user.is_staff:
            return Response({"error": "صلاحيات مطلوبة"}, status=403)
        review = self.get_object()
        review.is_approved = True
        review.save()
        notify(review.user, REVIEW_APPROVED(review.course.title), type=REVIEW)
        return Response({"message": "تمت الموافقة بنجاح"}, status=200)


class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام ID للإشعارات - zaki alkholy

    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user).order_by(
            "-created_at"
        )

    @action(detail=True, methods=["patch"], url_path="mark_as_read")
    def mark_as_read(self, request, pk=None):
        notification = self.get_object()
        if notification.is_read:
            return Response({"message": "الإشعار مقروء بالفعل"})
        notification.is_read = True
        notification.save()
        return Response({"message": "تم تعليم الإشعار كمقروء"})


class DigitalProductViewSet(viewsets.ModelViewSet):
    queryset = DigitalProduct.objects.all()
    serializer_class = DigitalProductSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            return DigitalProduct.objects.filter(is_published=True)
        return DigitalProduct.objects.filter(is_published=True)

    def perform_create(self, serializer):
        serializer.save(seller=self.request.user)



