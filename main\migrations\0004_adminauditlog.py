# Generated by Django 4.2.20 on 2025-07-29 20:05

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("main", "0003_order_base_price_order_paymob_fee_order_platform_fee_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AdminAuditLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("admin_username", models.CharField(max_length=150)),
                (
                    "admin_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("dashboard_login", "تسجيل دخول Dashboard"),
                            ("dashboard_logout", "تسجيل خروج Dashboard"),
                            ("dashboard_view", "عرض Dashboard"),
                            ("payout_created", "إنشاء تحويل"),
                            ("payout_processed", "معالجة تحويل"),
                            ("payout_completed", "إكمال تحويل"),
                            ("payout_cancelled", "إلغاء تحويل"),
                            ("receipt_uploaded", "رفع وصل تحويل"),
                            ("user_created", "إنشاء مستخدم"),
                            ("user_updated", "تحديث مستخدم"),
                            ("user_deleted", "حذف مستخدم"),
                            ("user_activated", "تفعيل مستخدم"),
                            ("user_deactivated", "إلغاء تفعيل مستخدم"),
                            ("course_approved", "الموافقة على كورس"),
                            ("course_rejected", "رفض كورس"),
                            ("course_suspended", "تعليق كورس"),
                            ("course_deleted", "حذف كورس"),
                            ("order_refunded", "استرداد طلب"),
                            ("order_cancelled", "إلغاء طلب"),
                            ("settings_changed", "تغيير إعدادات"),
                            ("backup_created", "إنشاء نسخة احتياطية"),
                            ("data_exported", "تصدير بيانات"),
                            ("permission_granted", "منح صلاحية"),
                            ("permission_revoked", "سحب صلاحية"),
                            ("security_alert", "تنبيه أمني"),
                        ],
                        max_length=50,
                    ),
                ),
                ("action_description", models.TextField()),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "منخفض"),
                            ("medium", "متوسط"),
                            ("high", "عالي"),
                            ("critical", "حرج"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                (
                    "target_model",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "target_object_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "target_object_repr",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                ("old_values", models.JSONField(blank=True, null=True)),
                ("new_values", models.JSONField(blank=True, null=True)),
                ("additional_data", models.JSONField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("session_key", models.CharField(blank=True, max_length=40, null=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "admin_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="admin_actions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "سجل مراجعة الإدارة",
                "verbose_name_plural": "سجلات مراجعة الإدارة",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["admin_user", "timestamp"],
                        name="main_admina_admin_u_6d9a3c_idx",
                    ),
                    models.Index(
                        fields=["action_type", "timestamp"],
                        name="main_admina_action__533719_idx",
                    ),
                    models.Index(
                        fields=["target_model", "target_object_id"],
                        name="main_admina_target__c9d011_idx",
                    ),
                    models.Index(
                        fields=["severity", "timestamp"],
                        name="main_admina_severit_fea459_idx",
                    ),
                ],
            },
        ),
    ]
