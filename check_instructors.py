#!/usr/bin/env python3
"""
Check instructor wallet and payment information
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "Newmnasa.settings")
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()


def check_instructors():
    """Check all instructors for wallet and payment info"""
    print("🔍 Checking Instructor Payment Information...")
    print("=" * 60)

    instructors = User.objects.filter(is_instructor=True)
    print(f"Total instructors: {instructors.count()}")
    print()

    for i, instructor in enumerate(instructors, 1):
        print(f"{i}. {instructor.username}")
        print(f"   Email: {instructor.email}")
        print(f"   Wallet Number: {instructor.wallet_number or 'NOT SET'}")
        print(f"   Payment Method: {instructor.payment_method or 'NOT SET'}")
        print(f"   Has Wallet: {instructor.has_wallet}")

        # Check if can create courses
        can_create = bool(instructor.wallet_number and instructor.payment_method)
        status = "✅ CAN CREATE COURSES" if can_create else "❌ CANNOT CREATE COURSES"
        print(f"   Status: {status}")
        print()


def fix_instructor_payment_info():
    """Fix missing payment info for instructors"""
    print("🔧 Fixing Instructor Payment Information...")
    print("=" * 60)

    from django.db import models

    instructors_without_payment = User.objects.filter(is_instructor=True).filter(
        models.Q(wallet_number__isnull=True) | models.Q(payment_method__isnull=True)
    )

    print(
        f"Instructors without complete payment info: {instructors_without_payment.count()}"
    )

    for instructor in instructors_without_payment:
        print(f"Fixing {instructor.username}...")

        if not instructor.wallet_number:
            instructor.wallet_number = "01234567890"  # Default test wallet

        if not instructor.payment_method:
            instructor.payment_method = "vodafone_cash"  # Default payment method

        instructor.has_wallet = True
        instructor.save()

        print(f"   ✅ Updated {instructor.username}")


if __name__ == "__main__":
    from django.db import models

    print("🚀 Instructor Payment Info Checker")
    print("=" * 60)

    check_instructors()

    # Ask if user wants to fix issues
    print("Do you want to fix instructors without payment info? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response == "y":
            fix_instructor_payment_info()
            print("\n" + "=" * 60)
            print("After fixing:")
            check_instructors()
    except:
        print("Skipping fix...")

    print("✅ Check complete!")
