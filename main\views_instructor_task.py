from rest_framework import viewsets, permissions
from .models_instructor_task import InstructorTask
from .serializers_instructor_task import InstructorTaskSerializer


class InstructorTaskViewSet(viewsets.ModelViewSet):
    serializer_class = InstructorTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام UUID للحماية القصوى - <PERSON><PERSON>

    def get_queryset(self):
        return InstructorTask.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)
