from django.db.models.signals import post_migrate
from django.dispatch import receiver
from django.utils.text import slugify
from .models import MainCategory, Category


@receiver(post_migrate)
def create_main_categories(sender, **kwargs):
    main_categories = [
        {
            "name": "مواد دراسية",
            "slug": "school-subjects",
            "description": "مواد دراسية - تصنيف رئيسي",
        },
        {"name": "برمجة", "slug": "programming", "description": "برمجة - تصنيف رئيسي"},
        {"name": "التصميم", "slug": "design", "description": "التصميم - تصنيف رئيسي"},
    ]

    for item in main_categories:
        obj, created = MainCategory.objects.get_or_create(
            slug=item["slug"],
            defaults={"name": item["name"], "description": item["description"]},
        )
        print(f"MainCategory: {obj.name} | Created: {created}")


# تعطيل مؤقت للـ signal لحل مشكلة الـ migration - zaki alkh<PERSON>
# @receiver(post_migrate)
def create_school_subjects_disabled(sender, **kwargs):
    try:
        main_cat = MainCategory.objects.get(slug="school-subjects")
    except MainCategory.DoesNotExist:
        return  # تجاهل إذا لم توجد الفئة الرئيسية

    all_subcategories = [
        {"name": "Arabic", "description": "اللغة العربية"},
        {"name": "Religious Education", "description": "التربية الدينية"},
        {"name": "English", "description": "اللغة الإنجليزية"},
        {"name": "Math", "description": "الرياضيات"},
        {"name": "Science", "description": "العلوم"},
        {"name": "Social Studies", "description": "الدراسات الاجتماعية"},
        {"name": "Computer", "description": "الحاسب الآلي"},
        {"name": "Art", "description": "التربية الفنية"},
        {"name": "PE", "description": "التربية الرياضية"},
        {"name": "Vocational Skills", "description": "المهارات المهنية"},
        {"name": "Algebra", "description": "الجبر"},
        {"name": "Geometry", "description": "الهندسة"},
        {"name": "History", "description": "التاريخ"},
        {"name": "Geography", "description": "الجغرافيا"},
        {"name": "Second Language", "description": "اللغة الأجنبية الثانية"},
        {"name": "Physics", "description": "الفيزياء"},
        {"name": "Chemistry", "description": "الكيمياء"},
        {"name": "Biology", "description": "الأحياء"},
        {"name": "Geology", "description": "الجيولوجيا وعلوم البيئة"},
        {"name": "Philosophy", "description": "الفلسفة والمنطق"},
        {"name": "Psychology", "description": "علم النفس والاجتماع"},
        {"name": "Economics", "description": "الاقتصاد والإحصاء"},
        {"name": "Civics", "description": "التربية الوطنية"},
    ]

    for sub in all_subcategories:
        Category.objects.update_or_create(
            slug=slugify(sub["name"]),
            main_category=main_cat,
            defaults={
                "name": sub["name"],
                "description": sub["description"],
            },
        )


# @receiver(post_migrate)  # تعطيل مؤقت - zaki alkholy
def create_programming_categories_disabled(sender, **kwargs):
    try:
        main_cat = MainCategory.objects.get(slug="programming")
    except MainCategory.DoesNotExist:
        return

    # جميع الفئات الفرعية من جميع المجموعات الأصلية
    all_subcategories = [
        {"name": "Python", "description": "Python"},
        {"name": "JavaScript", "description": "JavaScript"},
        {"name": "Java", "description": "Java"},
        {"name": "C++", "description": "C++"},
        {"name": "C#", "description": "C#"},
        {"name": "PHP", "description": "PHP"},
        {"name": "Go", "description": "Go"},
        {"name": "Ruby", "description": "Ruby"},
        {"name": "Swift", "description": "Swift"},
        {"name": "Rust", "description": "Rust"},
        {"name": "Frontend", "description": "تطوير الواجهات الأمامية"},
        {"name": "React", "description": "مكتبة JavaScript لبناء واجهات المستخدم"},
        {"name": "Vue.js", "description": "مكتبة لبناء واجهات تفاعلية"},
        {"name": "Angular", "description": "إطار عمل كامل للـ Frontend"},
        {"name": "Tailwind CSS", "description": "مكتبة CSS جاهزة لتصميم الواجهات"},
        {"name": "Bootstrap", "description": "إطار تصميم واجهات جاهز وسريع"},
        {"name": "Backend", "description": "تطوير الخوادم والواجهات الخلفية"},
        {"name": "Django", "description": "إطار عمل قوي في Python لتطوير الويب"},
        {"name": "Flask", "description": "إطار خفيف لبناء REST APIs"},
        {"name": "Express", "description": "إطار عمل Node.js للخلفية"},
        {
            "name": "Laravel",
            "description": "إطار عمل PHP قوي لبناء تطبيقات متكاملة",
        },
        {"name": "Spring", "description": "إطار Java لتطوير تطبيقات قوية"},
        {"name": "ASP.NET", "description": "منصة .NET لتطبيقات الويب"},
    ]

    for sub in all_subcategories:
        Category.objects.update_or_create(
            slug=slugify(sub["name"]),
            main_category=main_cat,
            defaults={
                "name": sub["name"],
                "description": sub["description"],
            },
        )


# @receiver(post_migrate)  # تعطيل مؤقت - zaki alkholy
def create_design_categories_disabled(sender, **kwargs):
    try:
        main_cat = MainCategory.objects.get(slug="design")
    except MainCategory.DoesNotExist:
        return

    all_subcategories = [
        {
            "name": "Graphic Design",
            "description": "تصميم شعارات وهوية بصرية وواجهات",
        },
        {"name": "Photoshop", "description": "برنامج لتحرير الصور وتصميم الجرافيك"},
        {
            "name": "Illustrator",
            "description": "برنامج للرسم المتجهي وتصميم الشعارات",
        },
        {"name": "UI/UX Design", "description": "تصميم واجهات وتجربة المستخدم"},
        {"name": "Packaging Design", "description": "تصميم عبوات ومنتجات"},
        {"name": "Brand Identity", "description": "تصميم هوية العلامة التجارية"},
        {
            "name": "Social Media Design",
            "description": "تصميم منشورات السوشيال ميديا",
        },
        {
            "name": "Motion Graphics",
            "description": "موشن جرافيك وتحريك العناصر البصرية",
        },
        {"name": "Video Editing", "description": "مونتاج الفيديوهات بشكل احترافي"},
        {"name": "After Effects", "description": "برنامج المؤثرات البصرية والموشن"},
        {"name": "Premiere Pro", "description": "برنامج Adobe Premiere للمونتاج"},
        {"name": "Photography", "description": "فن التقاط الصور باحترافية"},
        {"name": "Lightroom", "description": "برنامج لتعديل الصور الاحترافي"},
        {"name": "Portrait Photography", "description": "تصوير بورتريه وأشخاص"},
        {
            "name": "Product Photography",
            "description": "تصوير منتجات لأغراض تسويقية",
        },
        {"name": "Event Photography", "description": "تصوير المناسبات والحفلات"},
        {"name": "Fashion Photography", "description": "تصوير الأزياء والموضة"},
        {
            "name": "Landscape Photography",
            "description": "تصوير الطبيعة والمناظر الخارجية",
        },
    ]

    for sub in all_subcategories:
        Category.objects.update_or_create(
            slug=slugify(sub["name"]),
            main_category=main_cat,
            defaults={
                "name": sub["name"],
                "description": sub["description"],
            },
        )
