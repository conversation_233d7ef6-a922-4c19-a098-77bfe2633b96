# Generated by Django 4.2.20 on 2025-07-29 19:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("main", "0002_instructor_payout"),
    ]

    operations = [
        migrations.AddField(
            model_name="order",
            name="base_price",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name="order",
            name="paymob_fee",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AddField(
            model_name="order",
            name="platform_fee",
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name="order",
            name="status",
            field=models.CharField(
                choices=[
                    ("completed", "Completed"),
                    ("cancelled", "Cancelled"),
                    ("refunded", "Refunded"),
                ],
                default="completed",
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="PaymentSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                ("paymob_order_id", models.CharField(max_length=100, unique=True)),
                ("payment_method", models.CharField(max_length=50)),
                ("phone_number", models.CharField(max_length=15)),
                ("base_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("paymob_fee", models.DecimalField(decimal_places=2, max_digits=10)),
                ("platform_fee", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "billing_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                (
                    "billing_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                (
                    "course",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_sessions",
                        to="main.course",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
