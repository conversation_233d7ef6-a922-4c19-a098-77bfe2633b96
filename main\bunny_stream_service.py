"""
خدمة Bunny Stream للفيديوهات المدفوعة مع التشفير الكامل - زكي الخولي
هذه الخدمة تتعامل مع رفع وإدارة الفيديوهات المحمية على Bunny Stream
"""

import requests
import tempfile
import os
import json
import time
import hashlib
import hmac
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class BunnyStreamService:
    """
    خدمة Bunny Stream للفيديوهات المدفوعة مع التشفير الكامل - زكي الخولي
    """
    
    def __init__(self):
        self.api_key = settings.BUNNY_STREAM["API_KEY"]
        self.library_id = settings.BUNNY_STREAM["LIBRARY_ID"]
        self.cdn_hostname = settings.BUNNY_STREAM["CDN_HOSTNAME"]
        self.base_url = settings.BUNNY_STREAM["BASE_URL"]
        self.stream_url = settings.BUNNY_STREAM["STREAM_URL"]
        
        # Headers للـ API requests - zaki alkholy
        self.headers = {
            "AccessKey": self.api_key,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def create_video(self, title, collection_id=None):
        """
        إنشاء فيديو جديد في Bunny Stream - زكي الخولي
        """
        try:
            url = f"{self.base_url}/library/{self.library_id}/videos"

            data = {
                "title": title,
                "collectionId": collection_id
            }

            response = requests.post(url, headers=self.headers, json=data)

            # معالجة أخطاء التوثيق بشكل خاص - زكي الخولي
            if response.status_code == 401:
                logger.error("خطأ في التوثيق مع Bunny Stream - تحقق من API Key - زكي الخولي")
                raise Exception("Authentication failed: Invalid API Key. Please check your Bunny Stream API credentials.")
            elif response.status_code == 403:
                logger.error("ليس لديك صلاحية للوصول لهذه المكتبة - زكي الخولي")
                raise Exception("Access denied: You don't have permission to access this library.")
            elif response.status_code == 404:
                logger.error(f"المكتبة غير موجودة: {self.library_id} - زكي الخولي")
                raise Exception(f"Library not found: {self.library_id}")

            response.raise_for_status()

            video_data = response.json()
            logger.info(f"تم إنشاء فيديو جديد: {video_data.get('guid')} - زكي الخولي")

            return {
                "video_id": video_data.get("guid"),
                "title": video_data.get("title"),
                "status": video_data.get("status"),
                "created": True
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"خطأ في الشبكة أثناء إنشاء الفيديو: {str(e)} - زكي الخولي")
            raise Exception(f"Network error while creating video: {str(e)}")
        except Exception as e:
            logger.error(f"فشل في إنشاء الفيديو: {str(e)} - زكي الخولي")
            raise Exception(f"Failed to create video: {str(e)}")
    
    def upload_video(self, video_file, title, lesson_id=None):
        """
        رفع فيديو إلى Bunny Stream مع التشفير الكامل - زكي الخولي
        """
        try:
            # 1. إنشاء فيديو جديد أولاً
            video_info = self.create_video(title)
            video_id = video_info["video_id"]
            
            # 2. حفظ الفيديو مؤقتاً
            temp_fd, temp_path = tempfile.mkstemp(suffix=".mp4")
            with os.fdopen(temp_fd, "wb") as f:
                f.write(video_file.read())
            
            # 3. رفع الفيديو
            upload_url = f"{self.base_url}/library/{self.library_id}/videos/{video_id}"
            
            upload_headers = {
                "AccessKey": self.api_key,
            }
            
            with open(temp_path, "rb") as video_data:
                response = requests.put(
                    upload_url, 
                    headers=upload_headers, 
                    data=video_data
                )
                response.raise_for_status()
            
            # 4. حذف الملف المؤقت
            os.remove(temp_path)
            
            # 5. تحديث إعدادات الفيديو للحماية القصوى - زكي الخولي
            self._configure_video_security(video_id, lesson_id)
            
            # 6. الحصول على معلومات الفيديو المحدثة
            video_details = self.get_video_details(video_id)
            
            logger.info(f"تم رفع الفيديو بنجاح: {video_id} - زكي الخولي")
            
            return {
                "video_id": video_id,
                "title": title,
                "status": "uploaded",
                "duration": video_details.get("length", 0),
                "thumbnail_url": video_details.get("thumbnailFileName"),
                "hls_url": f"https://{self.cdn_hostname}/play/{video_id}",
                "iframe_url": f"{self.stream_url}/embed/{self.library_id}/{video_id}",
                "upload_success": True
            }
            
        except Exception as e:
            logger.error(f"فشل في رفع الفيديو: {str(e)} - زكي الخولي")
            raise Exception(f"Failed to upload video: {str(e)}")
    
    def _configure_video_security(self, video_id, lesson_id=None):
        """
        تكوين إعدادات الأمان للفيديو - زكي الخولي
        """
        try:
            url = f"{self.base_url}/library/{self.library_id}/videos/{video_id}"
            
            # إعدادات الحماية القصوى - zaki alkholy
            security_config = {
                "title": f"Lesson Video {lesson_id}" if lesson_id else "Protected Video",
                # تفعيل جميع إعدادات الحماية
                "hasMP4Fallback": settings.BUNNY_STREAM["ENABLE_MP4_FALLBACK"],
                # إعدادات إضافية للحماية
                "metaTags": [
                    {"property": "og:title", "value": "منصة تعليمية محمية"},
                    {"property": "og:description", "value": "محتوى محمي - زكي الخولي"}
                ]
            }
            
            response = requests.post(url, headers=self.headers, json=security_config)
            response.raise_for_status()
            
            logger.info(f"تم تكوين الحماية للفيديو: {video_id} - زكي الخولي")
            
        except Exception as e:
            logger.warning(f"تحذير: فشل في تكوين الحماية: {str(e)} - زكي الخولي")
    
    def get_video_details(self, video_id):
        """
        الحصول على تفاصيل الفيديو - زكي الخولي
        """
        try:
            url = f"{self.base_url}/library/{self.library_id}/videos/{video_id}"
            
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"فشل في الحصول على تفاصيل الفيديو: {str(e)} - زكي الخولي")
            raise Exception(f"Failed to get video details: {str(e)}")
    
    def setup_library_watermark(self):
        """
        إعداد watermark ثابت على مستوى الـ library - زكي الخولي
        ملاحظة: Bunny Stream لا يدعم dynamic watermark لكل user
        """
        try:
            url = f"{self.base_url}/videolibrary/{self.library_id}/watermark"

            # إعداد watermark ثابت للمنصة - زكي الخولي
            watermark_config = {
                "watermarkPositionLeft": 10,
                "watermarkPositionTop": 10,
                "watermarkWidth": 150,
                "watermarkHeight": 50,
                "watermarkOpacity": 70
            }

            # رفع صورة watermark (يجب أن تكون موجودة)
            # هذا مثال - يحتاج ملف صورة فعلي

            response = requests.put(url, headers=self.headers, json=watermark_config)
            response.raise_for_status()

            logger.info(f"تم إعداد watermark للمكتبة - زكي الخولي")

        except Exception as e:
            logger.warning(f"تحذير: فشل في إعداد watermark: {str(e)} - زكي الخولي")

    def generate_secure_playback_url(self, video_id, student_name, expiry_hours=24):
        """
        إنشاء رابط مشاهدة آمن - زكي الخولي
        ملاحظة: Bunny Stream لا يدعم dynamic watermark في URL
        الـ watermark يكون ثابت على مستوى الـ library
        """
        try:
            # حساب وقت انتهاء الصلاحية
            expiry_timestamp = int(time.time()) + (expiry_hours * 3600)

            # إنشاء token آمن
            token_data = {
                "video_id": video_id,
                "student_name": student_name,
                "expiry": expiry_timestamp,
                "library_id": self.library_id
            }

            # إنشاء signature للحماية - zaki alkholy
            token_string = json.dumps(token_data, sort_keys=True)
            signature = hmac.new(
                self.api_key.encode(),
                token_string.encode(),
                hashlib.sha256
            ).hexdigest()

            # رابط المشاهدة الآمن (بدون parameters غير مدعومة) - زكي الخولي
            secure_url = (
                f"{self.stream_url}/embed/{self.library_id}/{video_id}"
                f"?token={signature}"
                f"&expires={expiry_timestamp}"
                f"&autoplay=false"
                f"&controls=true"
                f"&responsive=true"
            )

            logger.info(f"تم إنشاء رابط آمن للطالب {student_name} - زكي الخولي")

            return {
                "secure_url": secure_url,
                "expires_at": expiry_timestamp,
                "student_name": student_name,
                "video_id": video_id,
                "watermark_note": "Watermark ثابت على مستوى المكتبة - زكي الخولي"
            }

        except Exception as e:
            logger.error(f"فشل في إنشاء الرابط الآمن: {str(e)} - زكي الخولي")
            raise Exception(f"Failed to generate secure URL: {str(e)}")

    def generate_instructor_playback_url(self, video_id, instructor_name, expiry_hours=24):
        """
        إنشاء رابط مشاهدة للمعلم بدون autoplay - زكي الخولي
        """
        try:
            # حساب وقت انتهاء الصلاحية
            expiry_timestamp = int(time.time()) + (expiry_hours * 3600)

            # إنشاء token آمن للمعلم
            token_data = {
                "video_id": video_id,
                "instructor_name": instructor_name,
                "expiry": expiry_timestamp,
                "library_id": self.library_id,
                "role": "instructor"
            }

            # إنشاء signature للحماية - zaki alkholy
            token_string = json.dumps(token_data, sort_keys=True)
            signature = hmac.new(
                self.api_key.encode(),
                token_string.encode(),
                hashlib.sha256
            ).hexdigest()

            # رابط المشاهدة للمعلم (بدون autoplay) - زكي الخولي
            instructor_url = (
                f"{self.stream_url}/embed/{self.library_id}/{video_id}"
                f"?token={signature}"
                f"&expires={expiry_timestamp}"
                f"&autoplay=false"
                f"&controls=true"
                f"&responsive=true"
                f"&muted=false"
            )

            logger.info(f"تم إنشاء رابط للمعلم {instructor_name} - زكي الخولي")

            return {
                "instructor_url": instructor_url,
                "expires_at": expiry_timestamp,
                "instructor_name": instructor_name,
                "video_id": video_id
            }

        except Exception as e:
            logger.error(f"فشل في إنشاء رابط المعلم: {str(e)} - زكي الخولي")
            raise Exception(f"Failed to generate instructor URL: {str(e)}")
    
    def delete_video(self, video_id):
        """
        حذف فيديو من Bunny Stream - زكي الخولي
        """
        try:
            url = f"{self.base_url}/library/{self.library_id}/videos/{video_id}"
            
            response = requests.delete(url, headers=self.headers)
            response.raise_for_status()
            
            logger.info(f"تم حذف الفيديو: {video_id} - زكي الخولي")
            
            return {"deleted": True, "video_id": video_id}
            
        except Exception as e:
            logger.error(f"فشل في حذف الفيديو: {str(e)} - زكي الخولي")
            raise Exception(f"Failed to delete video: {str(e)}")
    
    def get_video_statistics(self, video_id):
        """
        الحصول على إحصائيات الفيديو - زكي الخولي
        """
        try:
            url = f"{self.base_url}/library/{self.library_id}/videos/{video_id}/statistics"
            
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"فشل في الحصول على الإحصائيات: {str(e)} - زكي الخولي")
            return {}
