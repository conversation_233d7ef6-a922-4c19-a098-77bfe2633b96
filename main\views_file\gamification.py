# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

# Django imports
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model

# Models
from ..models import (
    StudentPoints,
    PointsHistory,
    Achievement,
    StudentAchievement,
    StudentProgress,
    UserQuizAttempt,
    Course,
    Review,
)

# Serializers
from ..serializers_file.progress_serializer import (
    StudentPointsSerializer,
    PointsHistorySerializer,
    AchievementSerializer,
    StudentAchievementSerializer,
)

User = get_user_model()


class GamificationViewSet(viewsets.ViewSet):
    """ViewSet شامل لنظام النقاط والإنجازات"""
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def leaderboard(self, request):
        """لوحة المتصدرين"""
        period = request.query_params.get('period', 'all')  # all, month, week
        
        # تحديد الفترة الزمنية
        if period == 'week':
            start_date = timezone.now() - timedelta(days=7)
            points_filter = Q(points_history__created_at__gte=start_date)
        elif period == 'month':
            start_date = timezone.now() - timedelta(days=30)
            points_filter = Q(points_history__created_at__gte=start_date)
        else:
            points_filter = Q()
        
        # الحصول على أفضل الطلاب
        if period == 'all':
            top_students = StudentPoints.objects.select_related('student').order_by('-total_points')[:20]
            leaderboard_data = [
                {
                    "rank": idx + 1,
                    "student_id": str(student.student.id),
                    "student_name": student.student.username,
                    "points": student.total_points,
                    "level": student.get_current_level_display(),
                    "achievements_count": student.student.achievements.count(),
                }
                for idx, student in enumerate(top_students)
            ]
        else:
            # حساب النقاط للفترة المحددة
            students_points = PointsHistory.objects.filter(
                created_at__gte=start_date,
                action='earned'
            ).values('student').annotate(
                period_points=Sum('points')
            ).order_by('-period_points')[:20]
            
            leaderboard_data = []
            for idx, data in enumerate(students_points):
                try:
                    student = User.objects.get(id=data['student'])
                    student_points = StudentPoints.objects.get(student=student)
                    leaderboard_data.append({
                        "rank": idx + 1,
                        "student_id": str(student.id),
                        "student_name": student.username,
                        "period_points": data['period_points'],
                        "total_points": student_points.total_points,
                        "level": student_points.get_current_level_display(),
                    })
                except (User.DoesNotExist, StudentPoints.DoesNotExist):
                    continue
        
        # موقع الطالب الحالي
        current_student_rank = None
        if period == 'all':
            try:
                student_points = StudentPoints.objects.get(student=request.user)
                higher_students = StudentPoints.objects.filter(
                    total_points__gt=student_points.total_points
                ).count()
                current_student_rank = higher_students + 1
            except StudentPoints.DoesNotExist:
                current_student_rank = None
        
        return Response({
            "period": period,
            "leaderboard": leaderboard_data,
            "current_student_rank": current_student_rank,
            "total_students": StudentPoints.objects.count(),
        })
    
    @action(detail=False, methods=['get'])
    def achievements_catalog(self, request):
        """كتالوج جميع الإنجازات المتاحة"""
        # الإنجازات المكتسبة
        earned_achievements = StudentAchievement.objects.filter(
            student=request.user
        ).values_list('achievement_id', flat=True)
        
        # جميع الإنجازات
        all_achievements = Achievement.objects.filter(is_active=True)
        
        achievements_data = []
        for achievement in all_achievements:
            is_earned = achievement.id in earned_achievements
            
            # حساب التقدم نحو الإنجاز
            progress = self.calculate_achievement_progress(request.user, achievement)
            
            achievements_data.append({
                "id": str(achievement.id),
                "name": achievement.name,
                "description": achievement.description,
                "icon": achievement.icon,
                "achievement_type": achievement.get_achievement_type_display(),
                "required_count": achievement.required_count,
                "points_reward": achievement.points_reward,
                "is_earned": is_earned,
                "is_hidden": achievement.is_hidden,
                "progress": progress,
                "progress_percentage": min(100, (progress / achievement.required_count) * 100) if achievement.required_count > 0 else 0,
            })
        
        # تصنيف الإنجازات
        categorized = {
            "earned": [a for a in achievements_data if a["is_earned"]],
            "in_progress": [a for a in achievements_data if not a["is_earned"] and a["progress"] > 0],
            "locked": [a for a in achievements_data if not a["is_earned"] and a["progress"] == 0],
        }
        
        return Response({
            "all_achievements": achievements_data,
            "categorized": categorized,
            "stats": {
                "total_achievements": len(achievements_data),
                "earned_count": len(categorized["earned"]),
                "in_progress_count": len(categorized["in_progress"]),
                "locked_count": len(categorized["locked"]),
            }
        })
    
    def calculate_achievement_progress(self, student, achievement):
        """حساب التقدم نحو إنجاز معين"""
        achievement_type = achievement.achievement_type
        
        if achievement_type == "lessons_completed":
            return StudentProgress.objects.filter(
                student=student,
                status='completed'
            ).count()
        
        elif achievement_type == "quizzes_passed":
            return UserQuizAttempt.objects.filter(
                user=student,
                passed=True
            ).count()
        
        elif achievement_type == "courses_completed":
            # حساب الدورات المكتملة (جميع دروسها مكتملة)
            completed_courses = 0
            enrolled_courses = student.enrolled_courses.all()
            
            for course in enrolled_courses:
                total_lessons = course.lessons.count()
                completed_lessons = StudentProgress.objects.filter(
                    student=student,
                    course=course,
                    status='completed'
                ).count()
                
                if total_lessons > 0 and completed_lessons == total_lessons:
                    completed_courses += 1
            
            return completed_courses
        
        elif achievement_type == "login_streak":
            student_points = StudentPoints.objects.filter(student=student).first()
            return student_points.login_streak if student_points else 0
        
        elif achievement_type == "points_earned":
            student_points = StudentPoints.objects.filter(student=student).first()
            return student_points.total_points if student_points else 0
        
        elif achievement_type == "perfect_score":
            return UserQuizAttempt.objects.filter(
                user=student,
                score=100
            ).count()
        
        elif achievement_type == "review_given":
            return Review.objects.filter(
                user=student,
                is_approved=True
            ).count()
        
        return 0
    
    @action(detail=False, methods=['post'])
    def redeem_points(self, request):
        """استبدال النقاط بمكافآت"""
        points_to_spend = request.data.get('points', 0)
        reward_type = request.data.get('reward_type', '')
        
        try:
            student_points = StudentPoints.objects.get(student=request.user)
        except StudentPoints.DoesNotExist:
            return Response(
                {"error": "ملف النقاط غير موجود"},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # التحقق من توفر النقاط
        if student_points.available_points < points_to_spend:
            return Response(
                {"error": "النقاط المتاحة غير كافية"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # تحديد المكافآت المتاحة
        rewards = {
            "discount_10": {"points": 500, "description": "خصم 10% على دورة جديدة"},
            "certificate": {"points": 1000, "description": "شهادة تقدير مميزة"},
            "consultation": {"points": 2000, "description": "استشارة مجانية مع المعلم"},
            "course_unlock": {"points": 1500, "description": "فتح دورة مجانية"},
        }
        
        if reward_type not in rewards:
            return Response(
                {"error": "نوع المكافأة غير صحيح"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        reward = rewards[reward_type]
        if points_to_spend != reward["points"]:
            return Response(
                {"error": f"النقاط المطلوبة: {reward['points']}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # استخدام النقاط
        success = student_points.spend_points(points_to_spend, f"استبدال: {reward['description']}")
        
        if success:
            # هنا يمكن إضافة منطق تفعيل المكافأة
            # مثل إنشاء كوبون خصم أو إرسال إشعار للمعلم
            
            return Response({
                "message": "تم استبدال النقاط بنجاح",
                "reward": reward,
                "remaining_points": student_points.available_points,
            })
        else:
            return Response(
                {"error": "فشل في استبدال النقاط"},
                status=status.HTTP_400_BAD_REQUEST
            )


class PointsStoreAPIView(APIView):
    """متجر النقاط"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على المكافآت المتاحة في المتجر"""
        try:
            student_points = StudentPoints.objects.get(student=request.user)
            available_points = student_points.available_points
        except StudentPoints.DoesNotExist:
            available_points = 0
        
        # المكافآت المتاحة
        rewards = [
            {
                "id": "discount_10",
                "title": "خصم 10%",
                "description": "خصم 10% على أي دورة جديدة",
                "points_cost": 500,
                "icon": "🎫",
                "category": "discounts",
                "can_afford": available_points >= 500,
            },
            {
                "id": "discount_20",
                "title": "خصم 20%",
                "description": "خصم 20% على أي دورة جديدة",
                "points_cost": 1000,
                "icon": "🎟️",
                "category": "discounts",
                "can_afford": available_points >= 1000,
            },
            {
                "id": "certificate",
                "title": "شهادة تقدير",
                "description": "شهادة تقدير مميزة باسمك",
                "points_cost": 800,
                "icon": "🏆",
                "category": "certificates",
                "can_afford": available_points >= 800,
            },
            {
                "id": "consultation",
                "title": "استشارة مجانية",
                "description": "جلسة استشارة مجانية مع أي معلم",
                "points_cost": 2000,
                "icon": "💬",
                "category": "services",
                "can_afford": available_points >= 2000,
            },
            {
                "id": "course_unlock",
                "title": "دورة مجانية",
                "description": "احصل على دورة مجانية من اختيارك",
                "points_cost": 3000,
                "icon": "📚",
                "category": "courses",
                "can_afford": available_points >= 3000,
            },
            {
                "id": "priority_support",
                "title": "دعم فني مميز",
                "description": "دعم فني مميز لمدة شهر",
                "points_cost": 1200,
                "icon": "🛠️",
                "category": "services",
                "can_afford": available_points >= 1200,
            },
        ]
        
        # تصنيف المكافآت
        categories = {}
        for reward in rewards:
            category = reward["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(reward)
        
        return Response({
            "student_points": available_points,
            "rewards": rewards,
            "categories": categories,
            "affordable_rewards": [r for r in rewards if r["can_afford"]],
        })


class WeeklyLeaderboardAPIView(APIView):
    """لوحة المتصدرين الأسبوعية"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """لوحة المتصدرين للأسبوع الحالي"""
        # بداية الأسبوع (الاثنين)
        today = timezone.now().date()
        days_since_monday = today.weekday()
        week_start = today - timedelta(days=days_since_monday)
        week_start_datetime = timezone.make_aware(datetime.combine(week_start, datetime.min.time()))
        
        # النقاط المكتسبة هذا الأسبوع
        weekly_points = PointsHistory.objects.filter(
            created_at__gte=week_start_datetime,
            action='earned'
        ).values('student').annotate(
            weekly_total=Sum('points')
        ).order_by('-weekly_total')[:10]
        
        leaderboard = []
        for idx, data in enumerate(weekly_points):
            try:
                student = User.objects.get(id=data['student'])
                student_points = StudentPoints.objects.get(student=student)
                
                # النشاط الأسبوعي
                weekly_lessons = StudentProgress.objects.filter(
                    student=student,
                    last_accessed__gte=week_start_datetime
                ).count()
                
                weekly_quizzes = UserQuizAttempt.objects.filter(
                    user=student,
                    created_at__gte=week_start_datetime
                ).count()
                
                leaderboard.append({
                    "rank": idx + 1,
                    "student_name": student.username,
                    "weekly_points": data['weekly_total'],
                    "total_points": student_points.total_points,
                    "level": student_points.get_current_level_display(),
                    "weekly_activity": {
                        "lessons": weekly_lessons,
                        "quizzes": weekly_quizzes,
                    }
                })
            except (User.DoesNotExist, StudentPoints.DoesNotExist):
                continue
        
        # موقع الطالب الحالي
        current_student_weekly_points = PointsHistory.objects.filter(
            student=request.user,
            created_at__gte=week_start_datetime,
            action='earned'
        ).aggregate(total=Sum('points'))['total'] or 0
        
        current_student_rank = None
        for idx, entry in enumerate(leaderboard):
            if entry["weekly_points"] <= current_student_weekly_points:
                current_student_rank = idx + 1
                break
        
        if current_student_rank is None:
            current_student_rank = len(leaderboard) + 1
        
        return Response({
            "week_period": {
                "start": week_start,
                "end": week_start + timedelta(days=6),
            },
            "leaderboard": leaderboard,
            "current_student": {
                "rank": current_student_rank,
                "weekly_points": current_student_weekly_points,
            },
            "total_participants": len(weekly_points),
        })
