# Django REST framework
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

# Django imports
from django.db.models import Count, Avg, Sum, Q, F
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model

# Models
from ..models import (
    StudentProgress,
    StudentPoints,
    PointsHistory,
    Achievement,
    StudentAchievement,
    ReviewSchedule,
    Course,
    Lesson,
    UserQuizAttempt,
    Assignment,
    AssignmentSubmission,
    Enrollment,
)

# Serializers
from ..serializers_file.progress_serializer import (
    StudentProgressSerializer,
    StudentPointsSerializer,
    PointsHistorySerializer,
    StudentAchievementSerializer,
    StudentDashboardSerializer,
    UpdateProgressSerializer,
)
from ..serializers_file.review_schedule_serializer import (
    ReviewScheduleSerializer,
    DailyReviewSerializer,
    ReviewStatsSerializer,
)

User = get_user_model()


class StudentProgressViewSet(viewsets.ModelViewSet):
    """ViewSet لإدارة تقدم الطلاب"""
    serializer_class = StudentProgressSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'  # استخدام UUID للحماية القصوى - zaki alkholy
    
    def get_queryset(self):
        """الحصول على تقدم الطالب الحالي فقط"""
        return StudentProgress.objects.filter(student=self.request.user)
    
    def perform_create(self, serializer):
        """إنشاء سجل تقدم جديد"""
        serializer.save(student=self.request.user)
    
    @action(detail=False, methods=['post'])
    def update_progress(self, request):
        """تحديث تقدم الطالب في درس"""
        serializer = UpdateProgressSerializer(data=request.data)
        if serializer.is_valid():
            lesson_id = serializer.validated_data['lesson_id']
            watch_time = serializer.validated_data['watch_time']
            completion_percentage = serializer.validated_data['completion_percentage']
            status_value = serializer.validated_data.get('status')
            
            try:
                lesson = Lesson.objects.get(id=lesson_id)
                course = lesson.course
                
                # التحقق من تسجيل الطالب في الدورة
                if not course.students.filter(id=request.user.id).exists():
                    return Response(
                        {"error": "غير مسجل في هذه الدورة"},
                        status=status.HTTP_403_FORBIDDEN
                    )
                
                # الحصول على أو إنشاء سجل التقدم
                progress, created = StudentProgress.objects.get_or_create(
                    student=request.user,
                    lesson=lesson,
                    course=course,
                    defaults={
                        'started_at': timezone.now(),
                        'total_duration': lesson.duration * 60 if lesson.duration else 0,
                    }
                )

                # تحديث البيانات - zaki alkholy
                old_watch_time = progress.watch_time
                old_completion = progress.completion_percentage
                current_time = timezone.now()

                progress.watch_time = max(progress.watch_time, watch_time)
                progress.completion_percentage = max(progress.completion_percentage, completion_percentage)
                progress.last_accessed = current_time

                # تحديد ما إذا كانت هذه جلسة مشاهدة جديدة - zaki alkholy
                is_new_session = False

                if created:
                    # سجل جديد تماماً
                    is_new_session = True
                    progress.last_session_start = current_time
                elif progress.last_session_start is None:
                    # لا توجد جلسة سابقة مسجلة
                    is_new_session = True
                    progress.last_session_start = current_time
                else:
                    # التحقق من انتهاء الجلسة السابقة (أكثر من 30 دقيقة من عدم النشاط)
                    session_timeout = timedelta(minutes=progress.session_timeout_minutes)
                    time_since_last_session = current_time - progress.last_session_start

                    if time_since_last_session > session_timeout:
                        is_new_session = True
                        progress.last_session_start = current_time

                # زيادة view_count فقط للجلسات الجديدة - zaki alkholy
                if is_new_session:
                    progress.view_count += 1
                
                # تحديث الحالة
                if status_value:
                    progress.status = status_value
                elif completion_percentage >= 80:
                    progress.status = 'completed'
                    if not progress.completed_at:
                        progress.completed_at = timezone.now()
                elif completion_percentage > 0:
                    progress.status = 'in_progress'
                
                progress.save()
                
                return Response({
                    "message": "تم تحديث التقدم بنجاح",
                    "progress": StudentProgressSerializer(progress).data
                })
                
            except Lesson.DoesNotExist:
                return Response(
                    {"error": "الدرس غير موجود"},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def course_progress(self, request):
        """الحصول على تقدم الطالب في دورة معينة"""
        course_id = request.query_params.get('course_id')
        if not course_id:
            return Response(
                {"error": "معرف الدورة مطلوب"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            course = Course.objects.get(id=course_id)
            
            # التحقق من التسجيل
            if not course.students.filter(id=request.user.id).exists():
                return Response(
                    {"error": "غير مسجل في هذه الدورة"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # الحصول على تقدم جميع الدروس
            lessons = course.lessons.all().order_by('order')
            progress_data = []
            
            for lesson in lessons:
                try:
                    progress = StudentProgress.objects.get(
                        student=request.user,
                        lesson=lesson
                    )
                    progress_data.append(StudentProgressSerializer(progress).data)
                except StudentProgress.DoesNotExist:
                    # إنشاء سجل فارغ للدروس غير المبدوءة
                    progress_data.append({
                        "lesson": str(lesson.id),
                        "lesson_title": lesson.title,
                        "status": "not_started",
                        "completion_percentage": 0,
                        "watch_time": 0,
                    })
            
            # حساب التقدم الإجمالي
            total_lessons = len(lessons)
            completed_lessons = StudentProgress.objects.filter(
                student=request.user,
                course=course,
                status='completed'
            ).count()
            
            overall_progress = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
            
            return Response({
                "course_id": str(course.id),
                "course_title": course.title,
                "overall_progress": round(overall_progress, 2),
                "completed_lessons": completed_lessons,
                "total_lessons": total_lessons,
                "lessons_progress": progress_data,
            })
            
        except Course.DoesNotExist:
            return Response(
                {"error": "الدورة غير موجودة"},
                status=status.HTTP_404_NOT_FOUND
            )


class StudentPointsAPIView(APIView):
    """API لإدارة نقاط الطالب"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على نقاط الطالب"""
        points_profile, created = StudentPoints.objects.get_or_create(
            student=request.user
        )
        
        serializer = StudentPointsSerializer(points_profile, context={'request': request})
        return Response(serializer.data)


class PointsHistoryAPIView(APIView):
    """API لتاريخ النقاط"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على تاريخ النقاط"""
        history = PointsHistory.objects.filter(student=request.user)
        
        # فلترة حسب النوع
        action_type = request.query_params.get('action')
        if action_type:
            history = history.filter(action=action_type)
        
        # فلترة حسب التاريخ
        days = request.query_params.get('days', 30)
        try:
            days = int(days)
            start_date = timezone.now() - timedelta(days=days)
            history = history.filter(created_at__gte=start_date)
        except ValueError:
            pass
        
        # ترقيم الصفحات
        page_size = 20
        page = request.query_params.get('page', 1)
        try:
            page = int(page)
        except ValueError:
            page = 1
        
        start = (page - 1) * page_size
        end = start + page_size
        
        history_page = history[start:end]
        total_count = history.count()
        
        serializer = PointsHistorySerializer(history_page, many=True)
        
        return Response({
            "results": serializer.data,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "has_next": end < total_count,
        })


class StudentAchievementsAPIView(APIView):
    """API لإنجازات الطالب"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على إنجازات الطالب"""
        achievements = StudentAchievement.objects.filter(
            student=request.user
        ).select_related('achievement').order_by('-earned_at')
        
        serializer = StudentAchievementSerializer(achievements, many=True)
        
        # إحصائيات الإنجازات
        total_achievements = achievements.count()
        total_points_from_achievements = sum(
            a.achievement.points_reward for a in achievements
        )
        
        # الإنجازات المتاحة (لم يحصل عليها بعد)
        earned_achievement_ids = achievements.values_list('achievement_id', flat=True)
        available_achievements = Achievement.objects.filter(
            is_active=True,
            is_hidden=False
        ).exclude(id__in=earned_achievement_ids)
        
        return Response({
            "earned_achievements": serializer.data,
            "stats": {
                "total_earned": total_achievements,
                "total_points_earned": total_points_from_achievements,
                "available_count": available_achievements.count(),
            },
            "available_achievements": [
                {
                    "id": str(ach.id),
                    "name": ach.name,
                    "description": ach.description,
                    "icon": ach.icon,
                    "points_reward": ach.points_reward,
                    "required_count": ach.required_count,
                    "achievement_type": ach.get_achievement_type_display(),
                }
                for ach in available_achievements[:10]  # أول 10 إنجازات متاحة
            ]
        })


class StudentDashboardAPIView(APIView):
    """لوحة تحكم شاملة للطالب"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """الحصول على بيانات لوحة التحكم"""
        student = request.user
        
        # استخدام الـ serializer المجمع
        dashboard_data = StudentDashboardSerializer(student, context={'request': request}).data
        
        # إضافة إحصائيات إضافية
        
        # الدورات الحالية مع التقدم - zaki alkholy
        current_courses = []

        # استخدام نفس الطريقة المستخدمة في CourseViewSet - zaki alkholy
        from django.db.models import Q
        all_enrolled_courses = Course.objects.filter(
            Q(is_published=True) & Q(students=student)
        ).distinct()

        # إذا مفيش نتائج، نجرب بدون شرط is_published
        if not all_enrolled_courses.exists():
            all_enrolled_courses_no_published = Course.objects.filter(
                Q(students=student)
            ).distinct()

            if all_enrolled_courses_no_published.exists():
                all_enrolled_courses = all_enrolled_courses_no_published

        # إذا مفيش نتائج، نجرب الـ Enrollment model كبديل
        if not all_enrolled_courses.exists():
            enrolled_courses_enrollment = Course.objects.filter(
                enrollments__student=student,
                is_published=True
            ).distinct()

            if enrolled_courses_enrollment.exists():
                all_enrolled_courses = enrolled_courses_enrollment
            else:
                pass

        for course in all_enrolled_courses:
            total_lessons = course.lessons.count()
            completed_lessons = StudentProgress.objects.filter(
                student=student,
                course=course,
                status='completed'
            ).count()
            
            progress_percentage = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
            
            current_courses.append({
                "course_id": str(course.id),
                "title": course.title,
                "instructor": course.instructor.username,
                "progress_percentage": round(progress_percentage, 2),
                "completed_lessons": completed_lessons,
                "total_lessons": total_lessons,
                "last_accessed": StudentProgress.objects.filter(
                    student=student,
                    course=course
                ).order_by('-last_accessed').first().last_accessed if StudentProgress.objects.filter(
                    student=student,
                    course=course
                ).exists() else None,
            })
        
        # الاختبارات الأخيرة
        recent_quiz_attempts = UserQuizAttempt.objects.filter(
            user=student
        ).select_related('quiz__lesson__course').order_by('-created_at')[:5]
        
        recent_quizzes = [
            {
                "quiz_title": attempt.quiz.title,
                "course_title": attempt.quiz.lesson.course.title,
                "score": attempt.score,
                "passed": attempt.passed,
                "date": attempt.created_at,
            }
            for attempt in recent_quiz_attempts
        ]
        
        # الواجبات المطلوب تسليمها
        pending_assignments = AssignmentSubmission.objects.filter(
            student=student,
            status='draft'
        ).select_related('assignment__lesson__course')
        
        upcoming_assignments = [
            {
                "assignment_id": str(sub.assignment.id),
                "title": sub.assignment.title,
                "course_title": sub.assignment.lesson.course.title,
                "due_date": sub.assignment.due_date,
                "is_overdue": sub.assignment.is_overdue(),
            }
            for sub in pending_assignments[:5]
        ]
        
        return Response({
            **dashboard_data,
            "current_courses": current_courses,
            "recent_quizzes": recent_quizzes,
            "upcoming_assignments": upcoming_assignments,
        })


class StudentReviewScheduleAPIView(APIView):
    """API للمراجعة المتباعدة للطالب"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """الحصول على جدولة المراجعة للطالب"""
        student = request.user

        # استخدام الـ serializer للمراجعة اليومية
        daily_review_data = DailyReviewSerializer(student).data

        # إحصائيات المراجعة
        review_stats_data = ReviewStatsSerializer(student).data

        return Response({
            "daily_review": daily_review_data,
            "stats": review_stats_data,
        })

    @action(detail=False, methods=['post'])
    def create_review_item(self, request):
        """إضافة عنصر جديد للمراجعة"""
        from ..serializers_file.review_schedule_serializer import CreateReviewScheduleSerializer

        serializer = CreateReviewScheduleSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            review_schedule = serializer.save()
            return Response({
                "message": "تم إضافة العنصر للمراجعة بنجاح",
                "review_schedule": ReviewScheduleSerializer(review_schedule).data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class StudentLearningStreakAPIView(APIView):
    """API لتتبع سلسلة التعلم المتتالية"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """الحصول على إحصائيات سلسلة التعلم"""
        student = request.user

        # حساب سلسلة الأيام المتتالية
        points_profile, _ = StudentPoints.objects.get_or_create(student=student)
        current_streak = points_profile.login_streak

        # حساب أطول سلسلة (يحتاج لتطوير نموذج منفصل لتتبع هذا)
        longest_streak = current_streak  # مؤقتاً

        # النشاط الأسبوعي
        week_start = timezone.now() - timedelta(days=7)
        weekly_activity = []

        for i in range(7):
            date = (timezone.now() - timedelta(days=i)).date()

            # النشاط في هذا اليوم
            daily_progress = StudentProgress.objects.filter(
                student=student,
                last_accessed__date=date
            ).count()

            daily_quizzes = UserQuizAttempt.objects.filter(
                user=student,
                created_at__date=date
            ).count()

            weekly_activity.append({
                "date": date,
                "lessons_accessed": daily_progress,
                "quizzes_taken": daily_quizzes,
                "total_activity": daily_progress + daily_quizzes,
            })

        weekly_activity.reverse()  # ترتيب من الأقدم للأحدث

        # أهداف التعلم
        learning_goals = {
            "daily_lesson_goal": 2,  # هدف يومي: درسين
            "weekly_quiz_goal": 5,   # هدف أسبوعي: 5 اختبارات
            "monthly_course_goal": 1, # هدف شهري: دورة واحدة
        }

        # التقدم نحو الأهداف
        today = timezone.now().date()
        today_lessons = StudentProgress.objects.filter(
            student=student,
            last_accessed__date=today
        ).count()

        week_quizzes = UserQuizAttempt.objects.filter(
            user=student,
            created_at__gte=week_start
        ).count()

        month_start = timezone.now().replace(day=1)
        month_completed_courses = StudentProgress.objects.filter(
            student=student,
            status='completed',
            completed_at__gte=month_start
        ).values('course').distinct().count()

        goal_progress = {
            "daily_lessons": {
                "current": today_lessons,
                "goal": learning_goals["daily_lesson_goal"],
                "percentage": min(100, (today_lessons / learning_goals["daily_lesson_goal"]) * 100),
            },
            "weekly_quizzes": {
                "current": week_quizzes,
                "goal": learning_goals["weekly_quiz_goal"],
                "percentage": min(100, (week_quizzes / learning_goals["weekly_quiz_goal"]) * 100),
            },
            "monthly_courses": {
                "current": month_completed_courses,
                "goal": learning_goals["monthly_course_goal"],
                "percentage": min(100, (month_completed_courses / learning_goals["monthly_course_goal"]) * 100),
            },
        }

        return Response({
            "streak_info": {
                "current_streak": current_streak,
                "longest_streak": longest_streak,
                "last_activity_date": points_profile.last_login_date,
            },
            "weekly_activity": weekly_activity,
            "learning_goals": goal_progress,
            "motivational_message": self.get_motivational_message(current_streak),
        })

    def get_motivational_message(self, streak):
        """رسالة تحفيزية بناءً على السلسلة"""
        if streak == 0:
            return "ابدأ رحلة التعلم اليوم! 🚀"
        elif streak < 3:
            return f"رائع! استمر لـ {3 - streak} أيام أخرى لتحقيق هدفك الأول! 💪"
        elif streak < 7:
            return f"ممتاز! أنت في طريقك لإكمال أسبوع كامل! ({streak}/7) 🔥"
        elif streak < 30:
            return f"مذهل! سلسلة {streak} يوم! استمر نحو الشهر الكامل! ⭐"
        else:
            return f"أسطوري! {streak} يوم متتالي من التعلم! أنت مثال يُحتذى به! 🏆"


class StudentComparisonAPIView(APIView):
    """API لمقارنة أداء الطالب مع الآخرين"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """مقارنة أداء الطالب مع المتوسط العام"""
        student = request.user

        # نقاط الطالب
        student_points = StudentPoints.objects.filter(student=student).first()
        student_total_points = student_points.total_points if student_points else 0

        # متوسط النقاط لجميع الطلاب
        avg_points = StudentPoints.objects.aggregate(
            avg_points=Avg('total_points')
        )['avg_points'] or 0

        # ترتيب الطالب
        higher_students = StudentPoints.objects.filter(
            total_points__gt=student_total_points
        ).count()
        total_students = StudentPoints.objects.count()
        rank = higher_students + 1

        # مقارنة معدل الإكمال
        student_completion_rate = StudentProgress.objects.filter(
            student=student,
            status='completed'
        ).count()

        avg_completion_rate = StudentProgress.objects.filter(
            student__is_student=True
        ).values('student').annotate(
            completed_count=Count('id', filter=Q(status='completed'))
        ).aggregate(
            avg_completed=Avg('completed_count')
        )['avg_completed'] or 0

        # مقارنة أداء الاختبارات
        student_quiz_avg = UserQuizAttempt.objects.filter(
            user=student
        ).aggregate(avg_score=Avg('score'))['avg_score'] or 0

        global_quiz_avg = UserQuizAttempt.objects.aggregate(
            avg_score=Avg('score')
        )['avg_score'] or 0

        # نسبة التحسن
        improvement_percentage = 0
        if avg_points > 0:
            improvement_percentage = ((student_total_points - avg_points) / avg_points) * 100

        return Response({
            "student_stats": {
                "total_points": student_total_points,
                "completed_lessons": student_completion_rate,
                "average_quiz_score": round(student_quiz_avg, 2),
                "current_level": student_points.current_level if student_points else "bronze",
            },
            "comparison": {
                "rank": rank,
                "total_students": total_students,
                "percentile": round((1 - (rank - 1) / total_students) * 100, 2) if total_students > 0 else 0,
                "points_vs_average": {
                    "student": student_total_points,
                    "average": round(avg_points, 2),
                    "difference": student_total_points - avg_points,
                    "improvement_percentage": round(improvement_percentage, 2),
                },
                "completion_vs_average": {
                    "student": student_completion_rate,
                    "average": round(avg_completion_rate, 2),
                },
                "quiz_performance_vs_average": {
                    "student": round(student_quiz_avg, 2),
                    "average": round(global_quiz_avg, 2),
                },
            },
            "achievements_comparison": {
                "student_achievements": StudentAchievement.objects.filter(student=student).count(),
                "average_achievements": StudentAchievement.objects.values('student').annotate(
                    count=Count('id')
                ).aggregate(avg_count=Avg('count'))['avg_count'] or 0,
            }
        })
