from django.contrib.auth import get_user_model
from rest_framework import serializers
from .models import (
    User,
    InstructorProfile,
    Course,
    Order,
    Review,
    DigitalProduct,
    Payment,
    Lesson,
    Quiz,
    Question,
    Answer,
    Certificate,
    Announcement,
    FAQ,
    Category,
    MainCategory,
    ReviewComment,
    InstructorAvailability,
    Notification,
    UserQuizAttempt,
)
from .serializers_file.course_serializer import CourseReadSerializer
from .serializers_file.user_serializer import UserSerializer

User = get_user_model()


# -------------------- UserSerializer --------------------


class DigitalProductSerializer(serializers.ModelSerializer):
    seller = UserSerializer(read_only=True)

    class Meta:
        model = DigitalProduct
        fields = [
            "id",
            "title",
            "description",
            "price",
            "seller",
            "file",
            "thumbnail",
            "created_at",
            "updated_at",
            "is_published",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class OrderSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    course = CourseReadSerializer(read_only=True)
    product = DigitalProductSerializer(read_only=True)

    class Meta:
        model = Order
        fields = [
            "id",
            "user",
            "course",
            "product",
            "amount",
            "status",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class PaymentSerializer(serializers.ModelSerializer):
    order = OrderSerializer(read_only=True)

    class Meta:
        model = Payment
        fields = [
            "id",
            "order",
            "amount",
            "payment_method",
            "transaction_id",
            "status",
            "created_at",
        ]
        read_only_fields = ["id", "created_at"]


class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ["id", "message", "link", "created_at", "is_read"]
