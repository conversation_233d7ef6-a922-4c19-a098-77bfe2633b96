# =============================
# سكربت disburse_instructors: تحويل أرباح المعلمين اليومية
# الكاتب: Zaki <PERSON>oly (بتنفيذ Copilot)
# تم تعطيل هذا السكربت مؤقتاً حسب طلب المطور
# هذا السكربت يبحث عن الطلبات المكتملة اليوم والتي لم يتم تحويل أرباحها،
# ثم ينفذ التحويل أو يعرضه فقط في حالة --dry-run
# =============================

# from django.core.management.base import BaseCommand
# from django.utils import timezone
# from django.conf import settings
# from main.models import Order, InstructorProfile, InstructorPayout
# from main.models import Course
# import logging

# # إعداد اللوجر لعرض المعلومات في التيرمنال
# logger = logging.getLogger(__name__)

# # استيراد دالة التحويل (يجب أن تكون معرفة مسبقًا) - تم تعطيلها مؤقتاً - zaki alkholy
# # from main.utils import process_payout

# class Command(BaseCommand):
#     help = 'تحويل أرباح المعلمين اليومية بناءً على الطلبات المكتملة.'

#     def add_arguments(self, parser):
#         # إضافة خيار --dry-run لتجربة السكربت بدون تنفيذ التحويلات فعليًا
#         parser.add_argument(
#             '--dry-run',
#             action='store_true',
#             help='اعرض العمليات فقط بدون تنفيذ التحويلات فعليًا.'
#         )

#     def handle(self, *args, **options):
#         # -----------------------------
#         # الحصول على تاريخ اليوم الحالي بالتوقيت المحلي
#         # -----------------------------
#         today = timezone.now().date()
#         logger.info(f"تشغيل تحويل الأرباح ليوم: {today}")

#         # -----------------------------
#         # استخراج الطلبات المكتملة اليوم والتي لم يتم تحويل أرباحها
#         # -----------------------------
#         orders = Order.objects.filter(
#             status='completed',
#             created_at__date=today
#         ).exclude(payout__isnull=False)

#         logger.info(f"عدد الطلبات المستوفية للشروط: {orders.count()}")

#         # -----------------------------
#         # التأكد من عدم تنفيذ التحويلات في وضع التطوير
#         # -----------------------------
#         if settings.DEBUG and not options['dry_run']:
#             logger.warning("وضع DEBUG مفعل! لن يتم تنفيذ أي تحويلات فعلية.")
#             return

#         # -----------------------------
#         # جلب نسبة عمولة المنصة من الإعدادات
#         # -----------------------------
#         platform_fee_percentage = getattr(settings, 'PLATFORM_FEE_PERCENTAGE', 20)

#         for order in orders:
#             try:
#                 # -----------------------------
#                 # التأكد من عدم وجود تحويل سابق لهذا الطلب (أمان إضافي)
#                 # -----------------------------
#                 if hasattr(order, 'payout'):
#                     logger.info(f"تم تحويل الطلب مسبقًا: {order.id}")
#                     continue

#                 # -----------------------------
#                 # جلب بيانات المعلم والكورس
#                 # -----------------------------
#                 course = order.course
#                 instructor = course.instructor if course else None
#                 if not instructor:
#                     logger.warning(f"الطلب {order.id} ليس له معلم مرتبط. سيتم تخطيه.")
#                     continue

#                 instructor_profile = instructor

#                 # -----------------------------
#                 # حساب عمولة المنصة وصافي ربح المعلم
#                 # -----------------------------
#                 amount = order.amount
#                 platform_fee = round((amount * platform_fee_percentage) / 100, 2)
#                 net_amount = amount - platform_fee

#                 logger.info(f"طلب {order.id}: المبلغ = {amount}, عمولة المنصة = {platform_fee}, صافي المعلم = {net_amount}")

#                 # -----------------------------
#                 # تنفيذ التحويل أو العرض فقط في حالة dry-run
#                 # -----------------------------
#                 payout_status = 'success'
#                 error_message = ''
#                 if options['dry_run']:
#                     logger.info(f"[DRY RUN] لن يتم تنفيذ تحويل فعلي للطلب {order.id}")
#                 else:
#                     try:
#                         # تنفيذ التحويل الفعلي
#                         process_payout(instructor_profile, net_amount)
#                         logger.info(f"تم تحويل المبلغ للمعلم {instructor_profile.user.username} بنجاح.")
#                     except Exception as e:
#                         # في حالة حدوث خطأ أثناء التحويل
#                         payout_status = 'failed'
#                         error_message = str(e)
#                         logger.error(f"فشل تحويل الطلب {order.id}: {error_message}")

#                 # -----------------------------
#                 # إنشاء سجل التحويل في InstructorPayout
#                 # -----------------------------
#                 InstructorPayout.objects.create(
#                     instructor=instructor_profile,
#                     order=order,
#                     course=course,
#                     amount_paid=net_amount,
#                     platform_fee=platform_fee,
#                     status=payout_status,
#                     error_message=error_message
#                 )

#             except Exception as ex:
#                 logger.error(f"خطأ غير متوقع في الطلب {order.id}: {ex}")

#         logger.info("انتهى السكربت بنجاح.")

# =============================
# نهاية سكربت تحويل الأموال للمعلم - zaki alkholy
# =============================
