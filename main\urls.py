from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>

from .views_file.authentication_autherization import (
    RegisterView,
    LoginView,
    GoogleSocialLoginView,
    VerifyEmailView,
    ResendVerificationEmailView,
    RequestPasswordResetView,
    ResetPasswordView,
)

# من views/instructor.py
from .views_file.instructor import (
    InstructorProfileViewSet,
    InstructorAvailabilityViewSet,
    InstructorDetailAPIView,
    InstructorDashboardAPIView,
    InstructorStudentsAPIView,
    InstructorCoursesAPIView,
    InstructorPayoutsAPIView,
)

# من views/quize.py
from .views_file.quize import (
    QuizViewSet,
    QuestionViewSet,
    AnswerViewSet,
)

# من views/course.py
from .views_file.course import (
    CourseViewSet,
    LessonViewSet,
    LessonResourceUploadView,
    VideoPlayerView,
    CategoryViewSet,
    MainCategoryViewSet,
    PublicCoursesViewSet,  # الكورسات العامة بدون authentication - zaki alkholy
    PublicCategoryViewSet,  # الفئات العامة بدون authentication - zaki alkholy
)

# من views/__init__.py أو أي مكان عام
from .views import (
    UserViewSet,
    DigitalProductViewSet,
    OrderViewSet,
    PaymentViewSet,
    ReviewViewSet,
    NotificationViewSet,

)

from .views_instructor_task import InstructorTaskViewSet

# استيراد دوال الدفع - تم تعطيل process_payout مؤقتاً - zaki alkholy
from .payment_views import (
    create_payment_intent,
    paymob_webhook,
    payment_status_by_course,
)  # , process_payout

# من views الجديدة للميزات المتقدمة - zaki alkholy
from .views_file.analytics import (
    InstructorAnalyticsAPIView,
    CourseAnalyticsAPIView,
    StudentPerformanceAPIView,
    SalesAnalyticsAPIView,
    LessonAnalyticsDetailAPIView,
    InstructorDashboardStatsAPIView,
    TopPerformingContentAPIView,
)

from .views_file.student_progress import (
    StudentProgressViewSet,
    StudentPointsAPIView,
    PointsHistoryAPIView,
    StudentAchievementsAPIView,
    StudentDashboardAPIView,
    StudentReviewScheduleAPIView,
    StudentLearningStreakAPIView,
    StudentComparisonAPIView,
)

from .views_file.gamification import (
    GamificationViewSet,
    PointsStoreAPIView,
    WeeklyLeaderboardAPIView,
)

# Admin Dashboard Views
from .views_file.admin_dashboard import (
    AdminDashboardStatsAPIView,
    PendingPayoutsAPIView,
    ProcessPayoutAPIView,
    CompletedPayoutsAPIView,
    AllOrdersAPIView,
    UploadReceiptAPIView,
    ReversalPayoutAPIView,
    AdminAuditLogAPIView,
    InstructorEarningsDetailAPIView,
)

from .views_file.assignments import (
    AssignmentViewSet,
    AssignmentSubmissionViewSet,
    InstructorAssignmentAnalyticsAPIView,
)

from .views_file.spaced_repetition import (
    ReviewScheduleViewSet,
    DailyReviewAPIView,
    ReviewStatsAPIView,
    AutoReviewSchedulerAPIView,
    ReviewRecommendationsAPIView,
)

# from .views import LessonResourceUploadView

router = DefaultRouter()
router.register(r"users", UserViewSet)
router.register(r"courses", CourseViewSet)
router.register(
    r"public-courses", PublicCoursesViewSet, basename="public-courses"
)  # الكورسات العامة بدون authentication - zaki alkholy
router.register(
    r"public-categories", PublicCategoryViewSet, basename="public-categories"
)  # الفئات العامة بدون authentication - zaki alkholy
router.register(r"products", DigitalProductViewSet)
router.register(r"orders", OrderViewSet)
router.register(r"payments", PaymentViewSet)
router.register(r"lessons", LessonViewSet)
# router.register(r"categories", CategoryViewSet)
router.register(r"instructor-profiles", InstructorProfileViewSet)
router.register(r"reviews", ReviewViewSet)
router.register(r"availabilities", InstructorAvailabilityViewSet)
router.register(r"notifications", NotificationViewSet, basename="notification")
router.register(r"instructor-tasks", InstructorTaskViewSet, basename="instructor-task")
router.register(r"quizzes", QuizViewSet)  # <-- تسجيل QuizViewSet هنا
router.register(r"questions", QuestionViewSet)  # <-- تسجيل QuestionViewSet هنا
router.register(r"answers", AnswerViewSet)  # <-- تسجيل AnswerViewSet هنا
router.register(r"payments", PaymentViewSet, basename="payment")  # <-- تسجيل PaymentViewSet


router.register(r"main-categories", MainCategoryViewSet, basename="maincategory")
router.register(r"categories", CategoryViewSet, basename="category")

# تسجيل ViewSets الجديدة للميزات المتقدمة - zaki alkholy
router.register(
    r"student-progress", StudentProgressViewSet, basename="student-progress"
)
router.register(r"gamification", GamificationViewSet, basename="gamification")
router.register(r"assignments", AssignmentViewSet, basename="assignment")
router.register(
    r"assignment-submissions",
    AssignmentSubmissionViewSet,
    basename="assignment-submission",
)
router.register(r"review-schedules", ReviewScheduleViewSet, basename="review-schedule")
urlpatterns = [
    path("api/", include(router.urls)),
    path("api-auth/", include("rest_framework.urls")),
    path(
        "api/lessons/<uuid:pk>/upload-resource/",
        LessonResourceUploadView.as_view(),
        name="upload-lesson-resource",
    ),
    # Authentication URLs
    path("api/auth/register/", RegisterView.as_view(), name="register"),
    path("api/auth/login/", LoginView.as_view(), name="login"),
    # lesson Player URLs
    path(
        "lesson/<uuid:lesson_id>/video/", VideoPlayerView.as_view(), name="video-player"
    ),
    # Payment URLs
    path(
        "api/create-payment-intent/",
        create_payment_intent,
        name="create-payment-intent",
    ),
    path(
        "api/payment-status/<uuid:course_id>/",
        payment_status_by_course,
        name="payment-status-by-course",
    ),
    # =============================
    # URL تحويل الأموال للمعلم - تم تعطيله مؤقتاً - zaki alkholy
    # =============================
    # path(
    #     "api/process-payout/<uuid:instructor_id>/",
    #     process_payout,
    #     name="process-payout",
    # ),
    path("api/paymob-webhook/", paymob_webhook, name="paymob-webhook"),
    # Instructor Detail URL
    path(
        "api/instructors/<uuid:id>/",
        InstructorDetailAPIView.as_view(),
        name="instructor-detail",
    ),
    # Instructor Dashboard URL
    path(
        "api/instructor/dashboard/",
        InstructorDashboardAPIView.as_view(),
        name="instructor-dashboard",
    ),
    # Instructor Students URL - zaki alkholy
    path(
        "api/instructor/students/",
        InstructorStudentsAPIView.as_view(),
        name="instructor-students",
    ),
    # Instructor Payouts URL
    path(
        "api/instructor/payouts/",
        InstructorPayoutsAPIView.as_view(),
        name="instructor-payouts",
    ),
    # Hossam Google auth
    # path("api/auth/google/", GoogleSocialLoginView.as_view(), name="google-auth"),
    path("api/google-login/", GoogleSocialLoginView.as_view(), name="google-login"),
    path(
        "api/email-verification/", VerifyEmailView.as_view(), name="email-verification"
    ),
    path(
        "api/resend-verification/", ResendVerificationEmailView.as_view(), name="resend-verification"
    ),
    path(
        "api/auth/request-reset-password/",
        RequestPasswordResetView.as_view(),
        name="request-reset-password",
    ),
    path(
        "api/auth/reset-password/", ResetPasswordView.as_view(), name="reset-password"
    ),
    # =============================
    # URLs للميزات المتقدمة الجديدة - zaki alkholy
    # =============================
    # تحليلات المعلمين
    path(
        "api/instructor/analytics/",
        InstructorAnalyticsAPIView.as_view(),
        name="instructor-analytics",
    ),
    path(
        "api/instructor/courses/",
        InstructorCoursesAPIView.as_view(),
        name="instructor-courses",
    ),
    path(
        "api/instructor/analytics/course/<uuid:course_id>/",
        CourseAnalyticsAPIView.as_view(),
        name="course-analytics",
    ),
    path(
        "api/instructor/students/<uuid:course_id>/",
        StudentPerformanceAPIView.as_view(),
        name="student-performance",
    ),
    path(
        "api/instructor/sales/",
        SalesAnalyticsAPIView.as_view(),
        name="sales-analytics",
    ),
    path(
        "api/instructor/lesson/<uuid:lesson_id>/analytics/",
        LessonAnalyticsDetailAPIView.as_view(),
        name="lesson-analytics",
    ),
    path(
        "api/instructor/dashboard/stats/",
        InstructorDashboardStatsAPIView.as_view(),
        name="instructor-dashboard-stats",
    ),
    path(
        "api/instructor/top-content/",
        TopPerformingContentAPIView.as_view(),
        name="top-performing-content",
    ),
    path(
        "api/instructor/assignments/analytics/",
        InstructorAssignmentAnalyticsAPIView.as_view(),
        name="instructor-assignment-analytics",
    ),
    # لوحة تحكم الطلاب
    path(
        "api/student/dashboard/",
        StudentDashboardAPIView.as_view(),
        name="student-dashboard",
    ),
    path(
        "api/student/points/",
        StudentPointsAPIView.as_view(),
        name="student-points",
    ),
    path(
        "api/student/points/history/",
        PointsHistoryAPIView.as_view(),
        name="points-history",
    ),
    path(
        "api/student/achievements/",
        StudentAchievementsAPIView.as_view(),
        name="student-achievements",
    ),
    path(
        "api/student/learning-streak/",
        StudentLearningStreakAPIView.as_view(),
        name="learning-streak",
    ),
    path(
        "api/student/comparison/",
        StudentComparisonAPIView.as_view(),
        name="student-comparison",
    ),
    # متجر النقاط والمكافآت
    path(
        "api/points-store/",
        PointsStoreAPIView.as_view(),
        name="points-store",
    ),
    path(
        "api/leaderboard/weekly/",
        WeeklyLeaderboardAPIView.as_view(),
        name="weekly-leaderboard",
    ),
    # المراجعة المتباعدة
    path(
        "api/student/review/daily/",
        DailyReviewAPIView.as_view(),
        name="daily-review",
    ),
    path(
        "api/student/review/stats/",
        ReviewStatsAPIView.as_view(),
        name="review-stats",
    ),
    path(
        "api/student/review/auto-schedule/",
        AutoReviewSchedulerAPIView.as_view(),
        name="auto-review-scheduler",
    ),
    path(
        "api/student/review/recommendations/",
        ReviewRecommendationsAPIView.as_view(),
        name="review-recommendations",
    ),

    # Admin Dashboard URLs - Protected
    path("api/admin-dashboard/stats/", AdminDashboardStatsAPIView.as_view(), name="admin-dashboard-stats"),
    path("api/admin-dashboard/pending-payouts/", PendingPayoutsAPIView.as_view(), name="pending-payouts"),
    path("api/admin-dashboard/process-payout/<uuid:instructor_id>/", ProcessPayoutAPIView.as_view(), name="process-payout"),
    path("api/admin-dashboard/completed-payouts/", CompletedPayoutsAPIView.as_view(), name="completed-payouts"),
    path("api/admin-dashboard/all-orders/", AllOrdersAPIView.as_view(), name="all-orders"),
    path("api/admin-dashboard/upload-receipt/<int:payout_id>/", UploadReceiptAPIView.as_view(), name="upload-receipt"),
    path("api/admin-dashboard/reverse-payout/<int:payout_id>/", ReversalPayoutAPIView.as_view(), name="reverse-payout"),
    path("api/admin-dashboard/audit-log/", AdminAuditLogAPIView.as_view(), name="admin-audit-log"),
    path("api/admin-dashboard/instructor-earnings/<uuid:instructor_id>/", InstructorEarningsDetailAPIView.as_view(), name="instructor-earnings-detail"),

]
