from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone

from ..models import (
    Assignment,
    AssignmentSubmission,
    Lesson,
    Course,
)

from .user_serializer import UserSerializer

User = get_user_model()


class AssignmentSerializer(serializers.ModelSerializer):
    """سيريالايزر للواجبات التفاعلية"""
    lesson_title = serializers.CharField(source="lesson.title", read_only=True)
    course_title = serializers.CharField(source="lesson.course.title", read_only=True)
    instructor_name = serializers.CharField(source="lesson.course.instructor.username", read_only=True)
    is_overdue = serializers.SerializerMethodField()
    time_remaining = serializers.SerializerMethodField()
    submissions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Assignment
        fields = [
            "id",
            "lesson",
            "lesson_title",
            "course_title",
            "instructor_name",
            "title",
            "description",
            "instructions",
            "assignment_type",
            "max_score",
            "passing_score",
            "due_date",
            "late_submission_allowed",
            "late_penalty_percentage",
            "allowed_file_types",
            "max_file_size_mb",
            "is_published",
            "auto_grade",
            "is_overdue",
            "time_remaining",
            "submissions_count",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "lesson_title",
            "course_title",
            "instructor_name",
            "is_overdue",
            "time_remaining",
            "submissions_count",
            "created_at",
            "updated_at",
        ]
    
    def get_is_overdue(self, obj):
        """التحقق من انتهاء موعد التسليم"""
        return obj.is_overdue()
    
    def get_time_remaining(self, obj):
        """الوقت المتبقي للتسليم"""
        if obj.is_overdue():
            return None
        
        time_diff = obj.due_date - timezone.now()
        return {
            "days": time_diff.days,
            "hours": time_diff.seconds // 3600,
            "minutes": (time_diff.seconds % 3600) // 60,
            "total_seconds": time_diff.total_seconds(),
        }
    
    def get_submissions_count(self, obj):
        """عدد التسليمات"""
        return obj.submissions.filter(status="submitted").count()


class AssignmentSubmissionSerializer(serializers.ModelSerializer):
    """سيريالايزر لتسليم الواجبات"""
    assignment_title = serializers.CharField(source="assignment.title", read_only=True)
    student_name = serializers.CharField(source="student.username", read_only=True)
    final_score = serializers.SerializerMethodField()
    is_late = serializers.SerializerMethodField()
    file_url = serializers.SerializerMethodField()
    
    class Meta:
        model = AssignmentSubmission
        fields = [
            "id",
            "assignment",
            "student",
            "assignment_title",
            "student_name",
            "text_content",
            "file_upload",
            "file_url",
            "submission_notes",
            "score",
            "feedback",
            "status",
            "submitted_at",
            "graded_at",
            "is_late",
            "attempt_number",
            "final_score",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "assignment_title",
            "student_name",
            "is_late",
            "final_score",
            "file_url",
            "created_at",
            "updated_at",
        ]
    
    def get_final_score(self, obj):
        """الدرجة النهائية مع خصم التأخير"""
        return obj.calculate_final_score()
    
    def get_is_late(self, obj):
        """التحقق من التأخير"""
        if obj.submitted_at and obj.assignment.due_date:
            return obj.submitted_at > obj.assignment.due_date
        return False
    
    def get_file_url(self, obj):
        """رابط الملف المرفوع"""
        if obj.file_upload:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file_upload.url)
            return obj.file_upload.url
        return None
    
    def validate_file_upload(self, value):
        """التحقق من الملف المرفوع"""
        if not value:
            return value
        
        assignment = self.instance.assignment if self.instance else None
        if not assignment:
            # في حالة الإنشاء، نحتاج للحصول على الواجب من البيانات
            assignment_id = self.initial_data.get('assignment')
            if assignment_id:
                try:
                    assignment = Assignment.objects.get(id=assignment_id)
                except Assignment.DoesNotExist:
                    raise serializers.ValidationError("الواجب غير موجود")
        
        if assignment:
            # التحقق من نوع الملف
            allowed_types = assignment.allowed_file_types.split(',')
            file_extension = value.name.split('.')[-1].lower()
            if file_extension not in [t.strip().lower() for t in allowed_types]:
                raise serializers.ValidationError(
                    f"نوع الملف غير مسموح. الأنواع المسموحة: {assignment.allowed_file_types}"
                )
            
            # التحقق من حجم الملف
            max_size = assignment.max_file_size_mb * 1024 * 1024  # تحويل إلى بايت
            if value.size > max_size:
                raise serializers.ValidationError(
                    f"حجم الملف كبير جداً. الحد الأقصى: {assignment.max_file_size_mb} ميجابايت"
                )
        
        return value


class AssignmentSubmissionCreateSerializer(serializers.ModelSerializer):
    """سيريالايزر لإنشاء تسليم واجب جديد"""
    
    class Meta:
        model = AssignmentSubmission
        fields = [
            "assignment",
            "text_content",
            "file_upload",
            "submission_notes",
        ]
    
    def create(self, validated_data):
        """إنشاء تسليم جديد"""
        student = self.context['request'].user
        assignment = validated_data['assignment']
        
        # التحقق من موعد التسليم
        is_late = timezone.now() > assignment.due_date
        if is_late and not assignment.late_submission_allowed:
            raise serializers.ValidationError("انتهى موعد تسليم الواجب ولا يُسمح بالتسليم المتأخر")
        
        # حساب رقم المحاولة
        last_attempt = AssignmentSubmission.objects.filter(
            assignment=assignment,
            student=student
        ).order_by('-attempt_number').first()
        
        attempt_number = (last_attempt.attempt_number + 1) if last_attempt else 1
        
        # إنشاء التسليم
        submission = AssignmentSubmission.objects.create(
            student=student,
            assignment=assignment,
            text_content=validated_data.get('text_content', ''),
            file_upload=validated_data.get('file_upload'),
            submission_notes=validated_data.get('submission_notes', ''),
            is_late=is_late,
            attempt_number=attempt_number,
            status='submitted',
            submitted_at=timezone.now()
        )
        
        return submission


class AssignmentGradingSerializer(serializers.ModelSerializer):
    """سيريالايزر لتقييم الواجبات (للمعلمين)"""
    student_name = serializers.CharField(source="student.username", read_only=True)
    assignment_title = serializers.CharField(source="assignment.title", read_only=True)
    
    class Meta:
        model = AssignmentSubmission
        fields = [
            "id",
            "student_name",
            "assignment_title",
            "score",
            "feedback",
            "status",
        ]
    
    def update(self, instance, validated_data):
        """تحديث التقييم"""
        instance.score = validated_data.get('score', instance.score)
        instance.feedback = validated_data.get('feedback', instance.feedback)
        instance.status = 'graded'
        instance.graded_at = timezone.now()
        instance.save()
        
        # إضافة نقاط للطالب إذا نجح
        if instance.score and instance.score >= instance.assignment.passing_score:
            from ..models import StudentPoints
            points_profile, _ = StudentPoints.objects.get_or_create(student=instance.student)
            points_earned = max(10, int(instance.score / 10))  # نقاط بناءً على الدرجة
            points_profile.add_points(
                points_earned, 
                f"نجاح في واجب: {instance.assignment.title}"
            )
        
        return instance


class StudentAssignmentListSerializer(serializers.ModelSerializer):
    """سيريالايزر لقائمة واجبات الطالب"""
    assignment = AssignmentSerializer(read_only=True)
    final_score = serializers.SerializerMethodField()
    
    class Meta:
        model = AssignmentSubmission
        fields = [
            "id",
            "assignment",
            "status",
            "score",
            "final_score",
            "submitted_at",
            "graded_at",
            "is_late",
            "attempt_number",
        ]
    
    def get_final_score(self, obj):
        return obj.calculate_final_score()


class InstructorAssignmentStatsSerializer(serializers.Serializer):
    """سيريالايزر لإحصائيات واجبات المعلم"""
    assignment_id = serializers.UUIDField()
    assignment_title = serializers.CharField()
    total_submissions = serializers.IntegerField()
    graded_submissions = serializers.IntegerField()
    pending_submissions = serializers.IntegerField()
    average_score = serializers.FloatField()
    pass_rate = serializers.FloatField()
    late_submissions = serializers.IntegerField()
    
    def to_representation(self, instance):
        """تحويل البيانات للعرض"""
        data = super().to_representation(instance)
        
        # حساب نسبة النجاح
        if data['graded_submissions'] > 0:
            data['pass_rate'] = round(data['pass_rate'], 2)
        else:
            data['pass_rate'] = 0
        
        # تقريب متوسط الدرجات
        if data['average_score']:
            data['average_score'] = round(data['average_score'], 2)
        else:
            data['average_score'] = 0
        
        return data
