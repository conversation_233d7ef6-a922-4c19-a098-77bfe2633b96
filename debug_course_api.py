#!/usr/bin/env python3
"""
Script to debug the course creation API issue
"""
import requests
import json
import sys

# API Configuration
BASE_URL = "http://127.0.0.1:8000"
API_URL = f"{BASE_URL}/api"

def test_course_creation():
    """Test course creation with different scenarios"""
    
    print("🔍 Testing Course Creation API...")
    print("=" * 50)
    
    # Test 1: Check if we can access the courses endpoint without authentication
    print("\n1. Testing unauthenticated access to courses endpoint:")
    try:
        response = requests.get(f"{API_URL}/courses/")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 2: Try to create a course without authentication
    print("\n2. Testing course creation without authentication:")
    course_data = {
        "title": "Test Course",
        "description": "Test course description",
        "price": 150.00,
        "category": 1,  # Assuming category with ID 1 exists
        "level": "beginner"
    }
    
    try:
        response = requests.post(
            f"{API_URL}/courses/",
            json=course_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Check available authentication endpoints
    print("\n3. Testing authentication endpoints:")
    auth_endpoints = [
        "/api/auth/login/",
        "/api/auth/register/",
        "/api-auth/"
    ]
    
    for endpoint in auth_endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"   {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   {endpoint}: Error - {e}")
    
    # Test 4: Check if we can get categories (needed for course creation)
    print("\n4. Testing categories endpoint:")
    try:
        response = requests.get(f"{API_URL}/categories/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"   Found {len(categories)} categories")
            if categories:
                print(f"   First category: {categories[0]}")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 5: Check public categories endpoint
    print("\n5. Testing public categories endpoint:")
    try:
        response = requests.get(f"{API_URL}/public-categories/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"   Found {len(categories)} public categories")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")

def test_with_authentication():
    """Test with authentication if possible"""
    print("\n" + "=" * 50)
    print("🔐 Testing with Authentication...")
    print("=" * 50)
    
    # Try to login with test credentials
    login_data = {
        "username": "test_user",
        "password": "test123456"
    }
    
    try:
        response = requests.post(
            f"{API_URL}/auth/login/",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Login attempt status: {response.status_code}")
        print(f"Login response: {response.text}")
        
        if response.status_code == 200:
            auth_data = response.json()
            token = auth_data.get('access') or auth_data.get('token')
            
            if token:
                print(f"✅ Authentication successful!")
                
                # Test authenticated course creation
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                course_data = {
                    "title": "Authenticated Test Course",
                    "description": "Test course with authentication",
                    "price": 150.00,
                    "level": "beginner"
                }
                
                print("\n6. Testing authenticated course creation:")
                response = requests.post(
                    f"{API_URL}/courses/",
                    json=course_data,
                    headers=headers
                )
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text}")
                
                # Check user profile
                print("\n7. Checking user profile:")
                response = requests.get(
                    f"{API_URL}/users/me/",  # or similar endpoint
                    headers=headers
                )
                print(f"   Status: {response.status_code}")
                if response.status_code == 200:
                    user_data = response.json()
                    print(f"   User: {user_data.get('username')}")
                    print(f"   Is instructor: {user_data.get('is_instructor')}")
                    print(f"   Wallet number: {user_data.get('wallet_number')}")
                    print(f"   Payment method: {user_data.get('payment_method')}")
                else:
                    print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"Authentication error: {e}")

def main():
    """Main function"""
    print("🚀 Course API Debug Tool")
    print("=" * 50)
    
    test_course_creation()
    test_with_authentication()
    
    print("\n" + "=" * 50)
    print("✅ Debug complete!")

if __name__ == "__main__":
    main()
