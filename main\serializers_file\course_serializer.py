from rest_framework import serializers
from django.contrib.auth import get_user_model

from ..models import (
    Review,
    Course,
    Lesson,
    Category,
    MainCategory,
    ReviewComment,
    Certificate,
    Announcement,
    FAQ,
    Quiz,  # ✅ مستخدم في LessonSerializer.get_quizzes
)

from .user_serializer import (
    UserSerializer,
)  # ✅ مستخدم في ReviewSerializer و CourseRead و غيرهم
from .quize_serializer import (
    QuizSerializer,
)  # ✅ مستخدم في LessonSerializer.get_quizzes


# -------------------- ReviewSerializer --------------------
class ReviewSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    course = serializers.SlugRelatedField(
        slug_field='slug',
        queryset=Course.objects.all()
    )  # تم تغييره لاستخدام slug بدلاً من UUID - zaki alkholy

    class Meta:
        model = Review
        fields = [
            "id",
            "course",
            "user",
            "rating",
            "comment",
            "created_at",
            "is_approved",
            "reply",
        ]
        read_only_fields = ["id", "user", "created_at"]


# -------------------- CourseReadSerializer --------------------
class CourseReadSerializer(serializers.ModelSerializer):
    instructor = UserSerializer(read_only=True)
    students_count = serializers.SerializerMethodField()
    rating = serializers.SerializerMethodField()
    reviews = ReviewSerializer(many=True, read_only=True)
    students = UserSerializer(many=True, read_only=True)
    likes_count = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = "__all__"
        read_only_fields = ["id", "created_at", "updated_at"]
        extra_kwargs = {"promo_video": {"required": False, "allow_null": True}}

    def get_students_count(self, obj):
        return obj.students.count()

    def get_rating(self, obj):
        reviews = obj.reviews.filter(is_approved=True)
        if reviews.exists():
            return round(sum([r.rating for r in reviews]) / reviews.count(), 2)
        return 0

    def get_likes_count(self, obj):
        return obj.likes.count()

    def get_is_liked(self, obj):
        user = self.context.get("request").user if self.context.get("request") else None
        if user and user.is_authenticated:
            return obj.likes.filter(id=user.id).exists()
        return False


# -------------------- CourseWriteSerializer --------------------
class CourseWriteSerializer(serializers.ModelSerializer):
    thumbnail = serializers.ImageField(
        required=False, allow_null=True, allow_empty_file=True
    )
    promo_video = serializers.FileField(
        required=False, allow_null=True, allow_empty_file=True
    )

    def validate_price(self, value):
        if value < 100:
            raise serializers.ValidationError("سعر الكورس يجب أن يكون 100 جنيه على الأقل.")
        return value

    def validate_discount_price(self, value):
        if value is not None and value < 100:
            raise serializers.ValidationError("سعر الخصم يجب أن يكون 100 جنيه على الأقل.")
        return value

    def validate(self, data):
        # التحقق من أن السعر بعد الخصم أقل من السعر الأصلي
        price = data.get('price', getattr(self.instance, 'price', 0) if self.instance else 0)
        discount_price = data.get('discount_price')

        if discount_price is not None and discount_price > 0:
            if discount_price >= price:
                raise serializers.ValidationError({
                    'discount_price': 'السعر بعد الخصم يجب أن يكون أقل من السعر الأصلي.'
                })

        return data

    class Meta:
        model = Course
        exclude = ["id", "created_at", "updated_at", "instructor", "students", "likes"]
        extra_kwargs = {
            "promo_video": {"required": False, "allow_null": True},
            "thumbnail": {"required": False, "allow_null": True},
        }


class LessonSerializer(serializers.ModelSerializer):
    video_info = serializers.SerializerMethodField()
    resources = serializers.SerializerMethodField()
    quizzes = (
        serializers.SerializerMethodField()
    )  # ✅ إضافة هذا السطر لعرض الامتحانات المرتبطة بالدرس

    def validate(self, data):
        return data

    def get_resources(self, obj):
        if obj.resources:
            return obj.resources.url
        return None

    def get_quizzes(self, obj):
        # جلب الامتحانات المرتبطة بالدرس
        quizzes = obj.quizzes.all()
        return QuizSerializer(quizzes, many=True, read_only=True).data

    class Meta:
        model = Lesson
        fields = [
            "id",
            "course",
            "title",
            "content",
            "lesson_type",
            "is_preview",
            "video",
            "video_info",
            "duration",
            "order",
            "resources",
            "quizzes",  # ✅ إضافة quizzes هنا
            "is_drm_protected",
            "is_hls_encrypted",
            "token_expiry_hours",
            "watermark_enabled",
            "video_platform",
        ]
        read_only_fields = ["video_info"]

    def get_video_info(self, obj):
        if not obj.video_public_id:
            return None
        return {
            "duration": obj.video_duration,
            "public_id": obj.video_public_id,
            "is_uploaded": bool(obj.video),
        }

    def get_video_url(self, obj):
        if obj.video:
            return obj.video.url
        return None


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ["id", "name", "slug", "description", "main_category"]


class MainCategorySerializer(serializers.ModelSerializer):
    subcategories = CategorySerializer(many=True, read_only=True)

    class Meta:
        model = MainCategory
        fields = ["id", "name", "slug", "description", "subcategories"]


class ReviewCommentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    replies = serializers.SerializerMethodField()

    class Meta:
        model = ReviewComment
        fields = ["id", "review", "user", "text", "created_at", "parent", "replies"]
        read_only_fields = ["id", "user", "created_at", "replies"]

    def get_replies(self, obj):
        # جلب الردود على هذا التعليق
        replies = obj.replies.all().order_by("created_at")
        return ReviewCommentSerializer(replies, many=True, context=self.context).data


class CertificateSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    course = CourseReadSerializer(read_only=True)

    class Meta:
        model = Certificate
        fields = [
            "id",
            "user",
            "course",
            "issued_at",
            "certificate_url",
            "verification_code",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class AnnouncementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Announcement
        fields = ["id", "course", "title", "content", "created_at", "updated_at"]
        read_only_fields = ["id", "created_at", "updated_at"]


class FAQSerializer(serializers.ModelSerializer):
    class Meta:
        model = FAQ
        fields = ["id", "course", "question", "answer", "order"]
        read_only_fields = ["id", "created_at", "updated_at"]
