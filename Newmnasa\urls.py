"""
URL configuration for Newmnasa project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/ comparable URLs/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from main import payment_views
from rest_framework_simplejwt.views import TokenBlacklistView

urlpatterns = [
    # تعطيل admin في الإنتاج للحماية - zaki alkholy
    # path("admin/", admin.site.urls),
    path("", include("main.urls")),
    path(
        "api/create-payment-intent/",
        payment_views.create_payment_intent,
        name="create_payment_intent",
    ),
    # =============================
    # URL تحويل الأموال للمعلم - تم تعطيله مؤقتاً - zaki alkholy
    # =============================
    # path(
    #     "api/process-payout/<uuid:instructor_id>/",
    #     payment_views.process_payout,
    #     name="process_payout",
    # ),
    path("api/paymob-webhook/", payment_views.paymob_webhook, name="paymob_webhook"),
    # Hossam Google auth
    # path("accounts/", include("allauth.urls")),  # مسارات Allauth
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

urlpatterns += [
    path("api/token/blacklist/", TokenBlacklistView.as_view(), name="token_blacklist"),
]

# تفعيل admin فقط في وضع التطوير - zaki alkholy
if settings.DEBUG:
    urlpatterns.insert(1, path("admin/", admin.site.urls))
